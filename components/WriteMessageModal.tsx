'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface WriteMessageModalProps {
  isOpen: boolean
  onClose: () => void
  recipientId: string
  recipientName: string
}

export function WriteMessageModal({ isOpen, onClose, recipientId, recipientName }: WriteMessageModalProps) {
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [sending, setSending] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const supabase = createSupabaseClient()

  const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedPhoto(file)
      const reader = new FileReader()
      reader.onload = (e) => setPhotoPreview(e.target?.result as string)
      reader.readAsDataURL(file)
    }
  }

  const removePhoto = () => {
    setSelectedPhoto(null)
    setPhotoPreview(null)
  }

  const uploadPhoto = async (file: File): Promise<string | null> => {
    try {
      setUploading(true)
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `dm-photos/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('dm-photos')
        .upload(filePath, file)

      if (uploadError) throw uploadError

      const { data: { publicUrl } } = supabase.storage
        .from('dm-photos')
        .getPublicUrl(filePath)

      return publicUrl
    } catch (error) {
      console.error('Error uploading photo:', error)
      return null
    } finally {
      setUploading(false)
    }
  }

  const handleSend = async () => {
    if (!message.trim() && !selectedPhoto) return

    setSending(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        alert('Please sign in to send messages')
        return
      }

      let photoUrl = null
      if (selectedPhoto) {
        photoUrl = await uploadPhoto(selectedPhoto)
        if (!photoUrl) {
          alert('Failed to upload photo')
          return
        }
      }

      // Determine message type
      let messageType = 'text'
      if (photoUrl && message.trim()) {
        messageType = 'text_with_photo'
      } else if (photoUrl) {
        messageType = 'photo'
      }

      // Send the message
      const { error: messageError } = await supabase
        .from('direct_messages')
        .insert({
          sender_id: user.id,
          recipient_id: recipientId,
          subject: subject.trim() || 'New Message',
          body: message.trim() || null,
          photo_url: photoUrl,
          photo_alt_text: selectedPhoto ? `Photo from ${user.email}` : null,
          message_type: messageType
        })

      if (messageError) {
        console.error('Error sending message:', messageError)
        alert('Failed to send message: ' + messageError.message)
        return
      }

      // Create notification for recipient
      const { data: senderProfile } = await supabase
        .from('users')
        .select('name')
        .eq('id', user.id)
        .single()

      await supabase
        .from('notifications')
        .insert({
          user_id: recipientId,
          type: 'message',
          title: 'New Message',
          body: `${senderProfile?.name || 'Someone'} sent you a message: "${subject || 'New Message'}"`,
          data: {
            sender_id: user.id,
            sender_name: senderProfile?.name,
            message_preview: message.substring(0, 100)
          }
        })

      // Reset form and close
      setSubject('')
      setMessage('')
      setSelectedPhoto(null)
      setPhotoPreview(null)
      onClose()
      alert('Message sent successfully!')

    } catch (error) {
      console.error('Error sending message:', error)
      alert('Failed to send message')
    } finally {
      setSending(false)
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden flex flex-col">
          
          {/* Header */}
          <div className="p-6 border-b border-gray-100 flex-shrink-0">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-serif text-gray-800">
                Write to {recipientName}
              </h2>
              <button 
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                ×
              </button>
            </div>
            <p className="text-gray-500 text-sm mt-1">
              Send a private message to this writer
            </p>
          </div>

          {/* Form */}
          <div className="p-6 space-y-4 flex-1 overflow-y-auto">
            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject (optional)
              </label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="What's this message about?"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 outline-none"
                maxLength={255}
              />
            </div>

            {/* Photo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Photo (optional)
              </label>

              {!photoPreview ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoSelect}
                    className="hidden"
                    id="photo-upload"
                  />
                  <label
                    htmlFor="photo-upload"
                    className="cursor-pointer flex flex-col items-center space-y-2"
                  >
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <span className="text-sm text-gray-600">Click to add a photo</span>
                  </label>
                </div>
              ) : (
                <div className="relative">
                  <img
                    src={photoPreview}
                    alt="Preview"
                    className="w-full h-32 sm:h-48 object-cover rounded-lg"
                  />
                  <button
                    onClick={removePhoto}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors text-sm"
                  >
                    ×
                  </button>
                </div>
              )}
            </div>

            {/* Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message {!selectedPhoto && <span className="text-red-500">*</span>}
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={selectedPhoto ? "Add a caption (optional)..." : "Write your message here..."}
                rows={selectedPhoto ? 3 : 4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 outline-none resize-none bg-white text-gray-900 placeholder-gray-500"
                maxLength={2000}
              />
              <div className="text-xs text-gray-500 mt-1">
                {message.length}/2000 characters
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-100 flex gap-3 justify-end flex-shrink-0 bg-white">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
              disabled={sending}
            >
              Cancel
            </button>
            <button
              onClick={handleSend}
              disabled={(!message.trim() && !selectedPhoto) || sending || uploading}
              className="bg-amber-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-amber-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {sending || uploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {uploading ? 'Uploading...' : 'Sending...'}
                </>
              ) : (
                <>
                  ✉️ Send Message
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
