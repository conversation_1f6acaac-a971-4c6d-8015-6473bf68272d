/**
 * Test buttons for OnlyDiary notification sounds
 * For development and testing purposes
 */

'use client'

import { useNotificationSounds } from '@/hooks/useNotificationSounds'

export function SoundTestButtons() {
  const { playPayment, playMessage, playCreation, toggleSounds, isSoundsEnabled } = useNotificationSounds()

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
      <h3 className="text-sm font-medium text-gray-900">🔊 Sound Test</h3>
      
      <div className="flex flex-wrap gap-2">
        <button
          onClick={playPayment}
          className="px-3 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
        >
          💰 Test Payment Sound
        </button>
        
        <button
          onClick={playMessage}
          className="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
        >
          💬 Test Message Sound
        </button>

        <button
          onClick={playCreation}
          className="px-3 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors"
        >
          ✨ Test Creation Sound
        </button>
        
        <button
          onClick={() => toggleSounds(!isSoundsEnabled())}
          className={`px-3 py-2 text-sm rounded-lg transition-colors ${
            isSoundsEnabled()
              ? 'bg-gray-600 text-white hover:bg-gray-700'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          {isSoundsEnabled() ? '🔊 Sounds On' : '🔇 Sounds Off'}
        </button>
      </div>
      
      <p className="text-xs text-gray-500">
        Test your notification sounds. Real sounds will play when you receive payments or messages.
      </p>
    </div>
  )
}
