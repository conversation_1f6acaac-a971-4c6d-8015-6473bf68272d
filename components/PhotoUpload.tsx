"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { photoStorage } from "@/lib/supabase/storage"
import { addWatermarkToFiles, validateImageFile } from "@/lib/watermark"
import Image from "next/image"

interface Photo {
  id: string
  url: string
  alt_text: string
  moderation_status: 'pending' | 'approved' | 'flagged' | 'rejected'
  created_at: string
}

interface PhotoUploadProps {
  entryId?: string
  onPhotosChange?: (photos: Photo[]) => void
  hasVideo?: boolean
  onMediaTypeChange?: (hasPhotos: boolean) => void
  zenMode?: boolean
}

export function PhotoUpload({ entryId, onPhotosChange, hasVideo, onMediaTypeChange, zenMode = false }: PhotoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [photos, setPhotos] = useState<Photo[]>([])
  const [error, setError] = useState("")
  const [autoSaving, setAutoSaving] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({})
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([])
  const [processingFiles, setProcessingFiles] = useState<string[]>([])
  const [watermarking, setWatermarking] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supabase = createSupabaseClient()

  const loadExistingPhotos = useCallback(async () => {
    if (!entryId) return

    setLoading(true)
    setError("")

    try {
      console.log('Loading photos for entry:', entryId)

      // Force refresh the session to ensure authentication
      await supabase.auth.refreshSession()

      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Authentication error when loading photos:', authError)
        setError("Please sign in to view photos")
        return
      }

      const { data: existingPhotos, error: loadError } = await supabase
        .from('photos')
        .select('*')
        .eq('diary_entry_id', entryId)
        .order('created_at', { ascending: true })

      if (loadError) {
        console.error('Error loading photos:', loadError)
        setError("Failed to load existing photos")
      } else {
        console.log('Loaded photos:', existingPhotos)
        setPhotos(existingPhotos || [])
        onPhotosChange?.(existingPhotos || [])
        onMediaTypeChange?.((existingPhotos || []).length > 0)
      }
    } catch (err) {
      console.error('Error loading photos:', err)
      setError("Failed to load existing photos")
    } finally {
      setLoading(false)
    }
  }, [entryId, supabase, onPhotosChange])

  // Load existing photos when entryId changes
  useEffect(() => {
    if (entryId) {
      loadExistingPhotos()
    } else {
      setPhotos([])
    }
  }, [entryId, loadExistingPhotos])



  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    if (photos.length + files.length > 20) {
      setError("Maximum 20 photos per entry")
      return
    }

    // Immediate feedback - show that we're starting
    setUploading(true)
    setError("Preparing your photos...")

    // Small delay to show the immediate feedback
    await new Promise(resolve => setTimeout(resolve, 100))

    try {
      // Step 1: Validate and watermark images
      const fileArray = Array.from(files)

      // Validate all files first
      for (const file of fileArray) {
        const validation = validateImageFile(file)
        if (!validation.valid) {
          throw new Error(validation.error)
        }
      }

      // Add watermarks to all images
      setWatermarking(true)
      setError("Adding watermarks...")

      console.log('Starting watermarking process for', fileArray.length, 'files')

      const watermarkedFiles = await addWatermarkToFiles(fileArray, {
        text: 'www.OnlyDiary.app',
        position: 'bottom-right',
        opacity: 0.7,
        fontSize: 16
      })

      console.log('Watermarking completed successfully')
      setWatermarking(false)
      setError("")

      // Ensure authentication is fresh before upload
      await supabase.auth.refreshSession()
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        throw new Error('Please sign in to upload photos')
      }

      let currentEntryId = entryId

      // If no entry exists, create one automatically
      if (!currentEntryId) {
        const { data: newEntry, error } = await supabase
          .from('diary_entries')
          .insert({
            title: 'Untitled Entry',
            body_md: '',
            user_id: user.id,
            is_free: true,
            is_hidden: true // Start as draft
          })
          .select()
          .single()

        if (error) {
          throw new Error('Failed to create entry for photos')
        }

        currentEntryId = newEntry.id
      }

      // Step 3: Upload watermarked photos to the saved entry with timeout and progress
      const fileNames = watermarkedFiles.map(file => file.name)
      setUploadingFiles(fileNames)

      const uploadPromises = watermarkedFiles.map(async (file) => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          throw new Error(`${file.name} is not an image file`)
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          throw new Error(`${file.name} is too large (max 10MB)`)
        }

        // Create unique filename
        const fileExt = file.name.split('.').pop()
        const fileName = `${currentEntryId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`

        // Set initial progress
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }))

        // Upload with timeout (30 seconds)
        const uploadPromise = photoStorage.upload(fileName, file)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Upload timeout for ${file.name}`)), 30000)
        )

        try {
          const { error: uploadError } = await Promise.race([uploadPromise, timeoutPromise]) as any

          if (uploadError) {
            throw new Error(`Failed to upload ${file.name}: ${uploadError.message}`)
          }

          // Update progress to 100%
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }))

          // Move to processing state
          setProcessingFiles(prev => [...prev, file.name])
        } catch (error: any) {
          // Remove from progress on error
          setUploadProgress(prev => {
            const newProgress = { ...prev }
            delete newProgress[file.name]
            return newProgress
          })
          throw error
        }

        // Get public URL
        const { data: { publicUrl } } = photoStorage.getPublicUrl(fileName)

        // Save photo as APPROVED immediately - user sees it right away
        const { data: photoData, error: photoError } = await supabase
          .from('photos')
          .insert({
            diary_entry_id: currentEntryId,
            url: publicUrl,
            alt_text: file.name,
            moderation_status: 'approved',
            created_at: new Date().toISOString()
          })
          .select('id, url, alt_text, moderation_status, created_at')
          .single()

        if (photoError) {
          // Remove from processing on error
          setProcessingFiles(prev => prev.filter(name => name !== file.name))
          throw new Error(`Failed to save photo record: ${photoError.message}`)
        }

        // Remove from processing - photo is now ready
        setProcessingFiles(prev => prev.filter(name => name !== file.name))

        // AWS moderation runs in background - doesn't affect user experience
        fetch('/api/moderate-photo', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ photoId: photoData.id })
        }).catch(() => {
          // Ignore errors - photo is already approved and visible
        })

        return photoData
      })

      console.log('Starting upload of', uploadPromises.length, 'photos')
      const uploadedPhotos = await Promise.all(uploadPromises)
      console.log('Upload completed successfully, uploaded', uploadedPhotos.length, 'photos')

      // Update state immediately for instant feedback
      const newPhotos = [...photos, ...uploadedPhotos]
      console.log('Setting photos state with', newPhotos.length, 'total photos')
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)
      onMediaTypeChange?.(newPhotos.length > 0)

      // Also refresh from database to ensure consistency
      if (currentEntryId) {
        console.log('Refreshing photos from database for entry:', currentEntryId)
        setTimeout(() => {
          loadExistingPhotos()
        }, 1000) // Small delay to ensure database is updated
      }

    } catch (err: any) {
      console.error('Photo upload error:', err)
      setError(err.message || "Failed to upload photos")
    } finally {
      setUploading(false)
      setWatermarking(false)
      setAutoSaving(false)
      setUploadingFiles([])
      setProcessingFiles([])
      setUploadProgress({})
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemovePhoto = async (photoId: string) => {
    try {
      const { error } = await supabase
        .from('photos')
        .delete()
        .eq('id', photoId)

      if (error) {
        setError("Failed to remove photo")
        return
      }

      const newPhotos = photos.filter(photo => photo.id !== photoId)
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)
      onMediaTypeChange?.(newPhotos.length > 0)
    } catch {
      setError("Failed to remove photo")
    }
  }

  return (
    <div className={`border-t p-6 ${
      zenMode ? 'border-slate-700/30' : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-lg font-medium ${
          zenMode ? 'text-white/90' : 'text-gray-900'
        }`}>Photos</h3>
        <span className={`text-sm ${
          zenMode ? 'text-white/60' : 'text-gray-500'
        }`}>{photos.length}/20</span>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {autoSaving && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">💾 Auto-saving your entry...</p>
        </div>
      )}



      {watermarking && (
        <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
          <p className="text-purple-600 text-sm">🎨 Adding watermarks to your photos...</p>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">Loading photos...</p>
        </div>
      )}

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm font-medium mb-3">
            📤 Uploading {uploadingFiles.length} photo{uploadingFiles.length > 1 ? 's' : ''}...
          </p>
          <div className="space-y-2">
            {uploadingFiles.map((fileName) => (
              <div key={fileName} className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="flex justify-between text-xs text-blue-600 mb-1">
                    <span className="truncate max-w-[200px]">{fileName}</span>
                    <span>{uploadProgress[fileName] || 0}%</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress[fileName] || 0}%` }}
                    ></div>
                  </div>
                </div>
                {uploadProgress[fileName] === 100 && (
                  <span className="text-green-600 text-xs">✓</span>
                )}
              </div>
            ))}
          </div>
          <p className="text-xs text-blue-500 mt-2">
            ⏱️ This may take up to 30 seconds per photo
          </p>
        </div>
      )}

      {/* Processing Progress */}
      {processingFiles.length > 0 && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-600 text-sm font-medium mb-3">
            ⚡ Processing {processingFiles.length} photo{processingFiles.length > 1 ? 's' : ''}...
          </p>
          <div className="space-y-2">
            {processingFiles.map((fileName) => (
              <div key={fileName} className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="text-xs text-green-600 mb-1">
                    <span className="truncate max-w-[200px]">{fileName}</span>
                  </div>
                  <div className="w-full bg-green-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full animate-pulse"></div>
                  </div>
                </div>
                <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ))}
          </div>
          <p className="text-xs text-green-500 mt-2">
            🔄 Finalizing and adding to your entry...
          </p>
        </div>
      )}

      {/* Upload Button */}
      <div className="mb-4">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || loading || watermarking || autoSaving || processingFiles.length > 0 || photos.length >= 20 || hasVideo}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {hasVideo ? "Video already added" :
           autoSaving ? "Saving entry..." :
           watermarking ? "Adding watermarks..." :
           uploading ? "Uploading..." :
           processingFiles.length > 0 ? "Processing..." :
           "Add Photos"}
        </button>
        {!entryId && (
          <p className="text-xs text-gray-500 mt-1">
            A new entry will be created when you upload photos
          </p>
        )}
      </div>

      {/* Photo Grid - Facebook style thumbnails */}
      {photos.length > 0 && (
        <div className="space-y-4">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group border border-gray-200 rounded-lg overflow-hidden flex justify-center">
              <Image
                src={photo.url}
                alt={photo.alt_text}
                width={400}
                height={320}
                className="max-w-full max-h-80 object-contain bg-gray-50"
              />
              <button
                onClick={() => handleRemovePhoto(photo.id)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                ×
              </button>
              {photo.moderation_status === 'flagged' && (
                <div className="absolute bottom-2 left-2 bg-orange-500 text-white text-xs px-2 py-1 rounded">
                  Flagged for Review
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
