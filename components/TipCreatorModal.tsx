'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'

interface TipCreatorModalProps {
  isOpen: boolean
  onClose: () => void
  creatorId: string
  creatorName: string
  bookId: string
  currentUserId: string
  onTipSuccess: (amount: number) => void
}

const TIP_AMOUNTS = [
  { value: 100, label: '$1.00' },
  { value: 300, label: '$3.00' },
  { value: 500, label: '$5.00' },
  { value: 1000, label: '$10.00' },
  { value: 2000, label: '$20.00' },
]

export function TipCreatorModal({
  isOpen,
  onClose,
  creatorId,
  creatorName,
  bookId,
  currentUserId,
  onTipSuccess
}: TipCreatorModalProps) {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState('')
  const [message, setMessage] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [showCustom, setShowCustom] = useState(false)

  const handleTip = async () => {
    if (!selectedAmount && !customAmount) return

    const tipAmount = selectedAmount || Math.round(parseFloat(customAmount) * 100)
    
    if (tipAmount < 100) {
      alert('Minimum tip amount is $1.00')
      return
    }

    setIsProcessing(true)

    try {
      const supabase = createSupabaseClient()

      // Create donation record
      const { data: donation, error: donationError } = await supabase
        .from('donations')
        .insert({
          donor_id: currentUserId,
          recipient_id: creatorId,
          amount: tipAmount,
          message: message.trim() || null,
          diary_entry_id: bookId, // Using book ID as reference
          payment_kind: 'donation'
        })
        .select()
        .single()

      if (donationError) {
        console.error('Error creating donation:', donationError)
        alert('Failed to process tip. Please try again.')
        return
      }

      // Create Stripe payment intent for the tip
      const response = await fetch('/api/donations/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          donationId: donation.id,
          amount: tipAmount,
          recipientId: creatorId,
          message: message.trim() || null
        }),
      })

      const { clientSecret, error } = await response.json()

      if (error) {
        console.error('Error creating payment intent:', error)
        alert('Failed to process payment. Please try again.')
        return
      }

      // Redirect to Stripe Checkout or handle payment
      // For now, we'll simulate success
      onTipSuccess(tipAmount)
      onClose()
      
      // Reset form
      setSelectedAmount(null)
      setCustomAmount('')
      setMessage('')
      setShowCustom(false)

    } catch (error) {
      console.error('Error processing tip:', error)
      alert('An error occurred. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCustomAmountChange = (value: string) => {
    // Only allow numbers and decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '')
    setCustomAmount(cleanValue)
    setSelectedAmount(null)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            💝 Tip {creatorName}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Description */}
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              Show your appreciation for this amazing book!
            </p>
            <p className="text-sm text-gray-500">
              Tips go directly to the creator and help support their work.
            </p>
          </div>

          {/* Amount Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Choose tip amount:
            </label>
            
            {/* Preset Amounts */}
            <div className="grid grid-cols-3 gap-2 mb-4">
              {TIP_AMOUNTS.map((amount) => (
                <button
                  key={amount.value}
                  onClick={() => {
                    setSelectedAmount(amount.value)
                    setCustomAmount('')
                    setShowCustom(false)
                  }}
                  className={`p-3 text-center border rounded-lg font-medium transition-colors ${
                    selectedAmount === amount.value
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  {amount.label}
                </button>
              ))}
            </div>

            {/* Custom Amount Toggle */}
            <button
              onClick={() => {
                setShowCustom(!showCustom)
                setSelectedAmount(null)
              }}
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              {showCustom ? 'Hide custom amount' : 'Enter custom amount'}
            </button>

            {/* Custom Amount Input */}
            {showCustom && (
              <div className="mt-3">
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <input
                    type="text"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    placeholder="0.00"
                    className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Minimum tip: $1.00
                </p>
              </div>
            )}
          </div>

          {/* Optional Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Add a message (optional):
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Thank you for this amazing book!"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none bg-white text-gray-900 placeholder-gray-500"
              maxLength={200}
            />
            <p className="text-xs text-gray-500 mt-1">
              {message.length}/200 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleTip}
              disabled={(!selectedAmount && !customAmount) || isProcessing}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              isLoading={isProcessing}
            >
              {isProcessing ? 'Processing...' : `Send Tip`}
            </Button>
          </div>

          {/* Fee Disclosure */}
          <div className="text-xs text-gray-500 text-center border-t border-gray-200 pt-4">
            <p>
              Platform fee: 5% • Stripe processing fee applies
            </p>
            <p className="mt-1">
              {selectedAmount || (customAmount && Math.round(parseFloat(customAmount) * 100)) ? 
                `Creator receives: $${(((selectedAmount || Math.round(parseFloat(customAmount) * 100)) * 0.95) / 100).toFixed(2)}` 
                : 'Creator receives 95% of tip amount'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
