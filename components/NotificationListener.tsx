/**
 * Real-time notification listener for OnlyDiary
 * Handles payment and message sound notifications
 */

'use client'

import { useEffect, useRef, useState } from 'react'
import { useNotificationSounds } from '@/hooks/useNotificationSounds'
import { createSupabaseClient } from '@/lib/supabase/client'

interface NotificationListenerProps {
  userId?: string
}

function NotificationListenerCore({ userId }: NotificationListenerProps) {
  const { playPayment, playMessage } = useNotificationSounds()
  const eventSourceRef = useRef<EventSource | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!userId) return

    // Set up Server-Sent Events connection
    const connectToNotifications = async () => {
      try {
        // Get auth token for SSE
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) return

        const eventSource = new EventSource('/api/notifications/stream')
        eventSourceRef.current = eventSource

        eventSource.onopen = () => {
          console.log('🔔 Connected to notification stream')
        }

        eventSource.onmessage = (event) => {
          try {
            const notification = JSON.parse(event.data)
            
            switch (notification.type) {
              case 'payment':
                console.log('💰 Payment notification received:', notification.data)
                playPayment()
                // You can add visual notification here too
                showPaymentToast(notification.data)
                break
                
              case 'subscription':
                console.log('📝 Subscription notification received:', notification.data)
                playPayment() // Use same sound for subscriptions
                showSubscriptionToast(notification.data)
                break
                
              case 'message':
                console.log('💬 Message notification received:', notification.data)
                playMessage()
                showMessageToast(notification.data)
                break
                
              case 'connected':
                console.log('✅ Notification stream connected for user:', notification.userId)
                break
                
              case 'heartbeat':
                // Silent heartbeat to keep connection alive
                break
                
              default:
                console.log('Unknown notification type:', notification.type)
            }
          } catch (error) {
            console.error('Error parsing notification:', error)
          }
        }

        eventSource.onerror = (error) => {
          console.error('Notification stream error:', error)
          // Reconnect after a delay
          setTimeout(connectToNotifications, 5000)
        }

      } catch (error) {
        console.error('Failed to connect to notifications:', error)
      }
    }

    connectToNotifications()

    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
        eventSourceRef.current = null
      }
    }
  }, [userId, playPayment, playMessage, supabase.auth])

  // Helper functions for visual notifications
  const showPaymentToast = (data: any) => {
    // You can integrate with your toast system here
    console.log(`💰 Payment received: $${(data.amount / 100).toFixed(2)}`)
  }

  const showSubscriptionToast = (data: any) => {
    console.log('📝 New subscriber!')
  }

  const showMessageToast = (data: any) => {
    console.log('💬 New message received')
  }

  // This component doesn't render anything visible
  return null
}

// Main component that gets the current user
export function NotificationListener() {
  const [userId, setUserId] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUserId(user?.id || null)
    }

    getCurrentUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUserId(session?.user?.id || null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  if (!userId) return null

  return <NotificationListenerCore userId={userId} />
}

// Hook for easy integration
export function useRealTimeNotifications(userId?: string) {
  const { playPayment, playMessage } = useNotificationSounds()

  useEffect(() => {
    if (!userId) return

    // Alternative approach using Supabase real-time directly
    const supabase = createSupabaseClient()

    const paymentsChannel = supabase
      .channel('user-payments')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'payments',
          filter: `writer_id=eq.${userId}`
        },
        (payload) => {
          console.log('💰 Real-time payment:', payload)
          playPayment()
        }
      )
      .subscribe()

    const subscriptionsChannel = supabase
      .channel('user-subscriptions')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'subscriptions',
          filter: `writer_id=eq.${userId}`
        },
        (payload) => {
          console.log('📝 Real-time subscription:', payload)
          playPayment()
        }
      )
      .subscribe()

    return () => {
      paymentsChannel.unsubscribe()
      subscriptionsChannel.unsubscribe()
    }
  }, [userId, playPayment, playMessage])
}
