'use client'

import Link from 'next/link'
import Image from 'next/image'
import { formatDistanceToNow } from 'date-fns'
import { Day1Badge } from '@/components/Day1Badge'
import { VideoThumbnail } from '@/components/VideoThumbnail'
import { CompactCommentsSection } from '@/components/CompactCommentsSection'
import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface TimelinePost {
  id: string
  content_type: 'diary' | 'book' | 'voice'
  title: string
  description: string
  is_free: boolean
  love_count: number
  view_count: number
  created_at: string
  featured?: boolean
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  // Content-specific data
  diary_entry?: {
    id: string
    photos?: Array<{ url: string; alt_text: string }>
    videos?: Array<{ r2_public_url: string; custom_thumbnail_url?: string }>
  }
  project?: {
    id: string
    cover_image_url?: string
    price_amount: number
    genre?: string
  }
  voice_post?: {
    id: string
    duration_seconds: number
    waveform_data?: any
  }
}

interface TimelineCardProps {
  post: TimelinePost
  hasAccess: boolean
  currentUserId?: string
}

export function TimelineCard({ post, hasAccess, currentUserId }: TimelineCardProps) {
  const [showComments, setShowComments] = useState(false)
  const [commentCount, setCommentCount] = useState(0)
  const supabase = createSupabaseClient()

  // Load comment count when component mounts
  useEffect(() => {
    if (post.content_type === 'diary' && post.diary_entry?.id) {
      loadCommentCount()
    }
  }, [post.diary_entry?.id])

  const loadCommentCount = async () => {
    if (!post.diary_entry?.id) return

    try {
      const { count, error } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('diary_entry_id', post.diary_entry.id)
        .eq('is_deleted', false)

      if (!error && count !== null) {
        setCommentCount(count)
      }
    } catch (err) {
      console.error('Error loading comment count:', err)
    }
  }
  const getContentIcon = () => {
    switch (post.content_type) {
      case 'diary': return '📖'
      case 'book': return '📚'
      case 'voice': return '🎙️'
      default: return '📄'
    }
  }
  
  const getContentLink = () => {
    switch (post.content_type) {
      case 'diary': return `/d/${post.diary_entry?.id}`
      case 'book': return `/books/${post.project?.id}`
      case 'voice': return `/voices/${post.voice_post?.id}`
      default: return '#'
    }
  }
  
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={`bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 overflow-hidden ${
      post.featured ? 'ring-2 ring-purple-100' : ''
    }`}>
      
      {/* Header */}
      <div className="p-4 pb-3">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
            {post.user.profile_picture_url || post.user.avatar ? (
              <Image
                src={post.user.profile_picture_url || post.user.avatar || ''}
                alt={post.user.name}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-500 font-medium">
                {post.user.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <Link
                href={`/u/${post.user.id}`}
                className="font-medium text-gray-900 hover:text-blue-600 truncate"
              >
                {post.user.name}
              </Link>
              {post.user.has_day1_badge && (
                <Day1Badge
                  signupNumber={post.user.signup_number}
                  badgeTier={post.user.badge_tier}
                  size="sm"
                  className="flex-shrink-0"
                />
              )}
              <span className="text-gray-400">•</span>
              <span className="text-sm text-gray-500">
                {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
              </span>
            </div>
            
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm">{getContentIcon()}</span>
              <span className="text-sm text-gray-600 capitalize">
                {post.content_type} {post.featured && '• Featured'}
              </span>
              {post.is_free && (
                <span className="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs font-medium">
                  FREE
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content Preview */}
      <Link href={getContentLink()} className="block">
        
        {/* Diary Entry */}
        {post.content_type === 'diary' && (
          <div className="px-4 pb-4">
            <h3 className="font-serif text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 line-clamp-2">
              {post.title}
            </h3>
            <p className="text-gray-600 text-sm line-clamp-3 mb-3">
              {post.description}
            </p>
            {/* Video thumbnail takes priority over photos */}
            {post.diary_entry?.videos?.[0] ? (
              <div className="flex justify-center">
                <div className="relative h-48 w-full max-w-md bg-gray-100 rounded-lg overflow-hidden">
                  <VideoThumbnail
                    videoUrl={post.diary_entry.videos[0].r2_public_url}
                    customThumbnailUrl={post.diary_entry.videos[0].custom_thumbnail_url}
                    alt={post.title}
                    className="w-full h-full"
                    timeInSeconds={1}
                    showPlayButton={true}
                    playButtonSize="md"
                  />
                </div>
              </div>
            ) : post.diary_entry?.photos?.[0] && (
              <div className="flex justify-center">
                <div className="relative h-48 w-full max-w-md bg-gray-100 rounded-lg overflow-hidden">
                  <Image
                    src={post.diary_entry.photos[0].url}
                    alt={post.diary_entry.photos[0].alt_text}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Book Release */}
        {post.content_type === 'book' && (
          <div className="px-4 pb-4">
            <div className="flex gap-4">
              {post.project?.cover_image_url && (
                <div className="w-20 h-28 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={post.project.cover_image_url}
                    alt={`${post.title} cover`}
                    width={80}
                    height={112}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="flex-1">
                <h3 className="font-serif text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-1 line-clamp-2">
                  {post.title}
                </h3>
                {post.project?.genre && (
                  <p className="text-sm text-gray-500 mb-2 capitalize">
                    {post.project.genre}
                  </p>
                )}
                <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                  {post.description}
                </p>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-gray-800">
                    {post.project?.price_amount === 0 
                      ? 'Free' 
                      : `$${(post.project?.price_amount! / 100).toFixed(2)}`
                    }
                  </span>
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                    New Release
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Voice Post */}
        {post.content_type === 'voice' && (
          <div className="px-4 pb-4">
            <h3 className="font-serif text-lg text-gray-800 mb-2 line-clamp-2">
              {post.title}
            </h3>
            {post.description && (
              <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                {post.description}
              </p>
            )}
            
            {/* Audio Player Preview */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <button className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors">
                  <span className="text-sm">▶️</span>
                </button>
                <div className="flex-1">
                  <div className="h-8 bg-gray-200 rounded-full mb-1">
                    {/* Waveform visualization would go here */}
                    <div className="h-full bg-blue-400 rounded-full" style={{ width: '30%' }}></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>0:00</span>
                    <span>{formatDuration(post.voice_post?.duration_seconds || 0)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Link>

      {/* Footer */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              ❤️ {post.love_count}
            </span>
            <span className="flex items-center gap-1">
              👁️ {post.view_count}
            </span>
          </div>

          {!hasAccess && !post.is_free && (
            <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">
              Subscription Required
            </span>
          )}
        </div>
      </div>

      {/* Comments Section - Only for diary entries */}
      {post.content_type === 'diary' && post.diary_entry?.id && hasAccess && (
        <CompactCommentsSection
          entryId={post.diary_entry.id}
          canComment={!!currentUserId}
          userId={currentUserId}
          isOpen={showComments}
          onToggle={() => setShowComments(!showComments)}
          commentCount={commentCount}
        />
      )}
    </div>
  )
}
