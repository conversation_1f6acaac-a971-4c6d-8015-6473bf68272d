"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Day1Badge } from "./Day1Badge"
import { ReactionSystem } from "./ReactionSystem"

interface Comment {
  id: string
  body: string
  created_at: string
  parent_comment_id?: string
  user: {
    id: string
    name: string
    avatar?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
  replies?: Comment[]
  depth?: number
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface BookCommentsSectionProps {
  bookId: string
  canComment: boolean
  userId?: string
  maxDepth?: number
  isCompact?: boolean
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Recursive function to build nested comment tree
function buildCommentTree(comments: Comment[]): Comment[] {
  const commentMap = new Map<string, Comment>()
  const rootComments: Comment[] = []

  // First pass: create map of all comments
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, replies: [], depth: 0 })
  })

  // Second pass: build tree structure
  comments.forEach(comment => {
    const commentWithReplies = commentMap.get(comment.id)!
    
    if (comment.parent_comment_id) {
      const parent = commentMap.get(comment.parent_comment_id)
      if (parent) {
        commentWithReplies.depth = (parent.depth || 0) + 1
        parent.replies!.push(commentWithReplies)
      }
    } else {
      rootComments.push(commentWithReplies)
    }
  })

  return rootComments
}

// Recursive component to render nested comments
function NestedComment({ 
  comment, 
  canComment, 
  userId, 
  onReply, 
  replyingTo, 
  setReplyingTo,
  replyText,
  setReplyText,
  submitting,
  maxDepth = 5,
  isCompact = false
}: {
  comment: Comment
  canComment: boolean
  userId?: string
  onReply: (parentId: string, text: string) => void
  replyingTo: string | null
  setReplyingTo: (id: string | null) => void
  replyText: string
  setReplyText: (text: string) => void
  submitting: boolean
  maxDepth?: number
  isCompact?: boolean
}) {
  const depth = comment.depth || 0
  const canReply = canComment && userId && depth < maxDepth
  const marginLeft = Math.min(depth * (isCompact ? 12 : 16), isCompact ? 48 : 64)

  return (
    <div className="space-y-2">
      <div 
        className="flex items-start space-x-2"
        style={{ marginLeft: `${marginLeft}px` }}
      >
        <div className={`${isCompact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0`}>
          {comment.user.avatar ? (
            <img
              src={comment.user.avatar}
              alt={comment.user.name}
              className={`${isCompact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full object-cover`}
            />
          ) : (
            <span className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-gray-600`}>
              {comment.user.name.charAt(0).toUpperCase()}
            </span>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className={`font-medium text-gray-900 ${isCompact ? 'text-xs' : 'text-sm'} truncate`}>
              {comment.user.name}
            </span>
            {comment.user.has_day1_badge && (
              <Day1Badge
                signupNumber={comment.user.signup_number}
                badgeTier={comment.user.badge_tier}
                size="sm"
                className="flex-shrink-0"
              />
            )}
            <span className={`${isCompact ? 'text-xs' : 'text-xs'} text-gray-500`}>
              {formatDate(comment.created_at)}
            </span>
          </div>

          <p className={`text-gray-700 ${isCompact ? 'text-xs' : 'text-sm'} leading-relaxed mb-2`}>
            {comment.body}
          </p>

          {/* Reaction System */}
          <div className="flex items-center gap-3 mb-1">
            <ReactionSystem
              contentId={comment.id}
              contentType="comment"
              currentUserId={userId}
              initialReactions={comment.reactions || {}}
              userReaction={comment.userReaction}
              onReactionUpdate={(reactions, userReaction) => {
                // The real-time subscription will handle updates
                // But we can also update locally for immediate feedback
                comment.reactions = reactions
                comment.userReaction = userReaction
              }}
            />

            {canReply && (
              <button
                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                className={`text-blue-600 hover:text-blue-700 ${isCompact ? 'text-xs' : 'text-xs'} font-medium`}
              >
                {replyingTo === comment.id ? 'Cancel' : 'Reply'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Reply Form */}
      {replyingTo === comment.id && canComment && userId && (
        <div 
          className="bg-blue-50 rounded-lg p-2"
          style={{ marginLeft: `${marginLeft + (isCompact ? 24 : 32)}px` }}
        >
          <form onSubmit={(e) => {
            e.preventDefault()
            if (replyText.trim()) {
              onReply(comment.id, replyText.trim())
            }
          }}>
            <textarea
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              placeholder={`Reply to ${comment.user.name}...`}
              className={`w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 ${isCompact ? 'text-xs' : 'text-sm'} resize-none focus:outline-none focus:ring-1 focus:ring-blue-500`}
              rows={2}
              disabled={submitting}
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-500">
                {replyText.length}/1000
              </span>
              <div className="flex gap-1">
                <button
                  type="button"
                  onClick={() => {
                    setReplyingTo(null)
                    setReplyText("")
                  }}
                  className="px-2 py-1 text-gray-600 hover:text-gray-800 text-xs"
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!replyText.trim() || submitting}
                  className="px-2 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? 'Posting...' : 'Reply'}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="space-y-2">
          {comment.replies.map((reply) => (
            <NestedComment
              key={reply.id}
              comment={reply}
              canComment={canComment}
              userId={userId}
              onReply={onReply}
              replyingTo={replyingTo}
              setReplyingTo={setReplyingTo}
              replyText={replyText}
              setReplyText={setReplyText}
              submitting={submitting}
              maxDepth={maxDepth}
              isCompact={isCompact}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function BookCommentsSection({
  bookId,
  canComment,
  userId,
  maxDepth = 5,
  isCompact = false
}: BookCommentsSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [newComment, setNewComment] = useState("")
  const [replyText, setReplyText] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()

  useEffect(() => {
    loadComments()

    // Set up real-time subscription for new comments and reactions
    const channel = supabase
      .channel(`book-comments-${bookId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'comments',
          filter: `book_id=eq.${bookId}`
        },
        (payload) => {
          console.log('New book comment received:', payload)
          setTimeout(() => {
            loadComments()
          }, 1000)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'comments',
          filter: `book_id=eq.${bookId}`
        },
        (payload) => {
          console.log('Book comment updated:', payload)
          setTimeout(() => {
            loadComments()
          }, 1000)
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reactions',
          filter: `comment_id=neq.null`
        },
        (payload) => {
          console.log('Comment reaction updated:', payload)
          // Reload comments to get updated reaction counts
          setTimeout(() => {
            loadComments()
          }, 500)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [bookId, supabase])

  const loadComments = async () => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          body,
          created_at,
          parent_comment_id,
          user:users!user_id (
            id,
            name,
            avatar,
            has_day1_badge,
            signup_number
          )
        `)
        .eq('book_id', bookId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error loading book comments:', error)
      } else {
        // Load reactions for each comment
        const commentsWithReactions = await Promise.all(
          (data || []).map(async (comment) => {
            // Get reaction counts for this comment
            const { data: reactions } = await supabase
              .from('reactions')
              .select('reaction_type')
              .eq('comment_id', comment.id)

            // Count reactions by type
            const reactionCounts: Record<string, number> = {}
            reactions?.forEach(reaction => {
              reactionCounts[reaction.reaction_type] = (reactionCounts[reaction.reaction_type] || 0) + 1
            })

            // Get user's reaction if logged in
            let userReaction = null
            if (userId) {
              const { data: userReactionData } = await supabase
                .from('reactions')
                .select('reaction_type')
                .eq('user_id', userId)
                .eq('comment_id', comment.id)
                .single()

              userReaction = userReactionData?.reaction_type || null
            }

            return {
              ...comment,
              reactions: reactionCounts,
              userReaction
            }
          })
        )

        const nestedComments = buildCommentTree(commentsWithReactions)
        setComments(nestedComments)
      }
    } catch (err) {
      console.error('Error loading book comments:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newComment.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          book_id: bookId,
          user_id: userId,
          body: newComment.trim()
        })

      if (error) {
        console.error('Book comment insert error:', error)
        setError(`Failed to post comment: ${error.message || 'Please try again.'}`)
      } else {
        setNewComment("")
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentCommentId: string, text: string) => {
    if (!text.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          book_id: bookId,
          user_id: userId,
          body: text,
          parent_comment_id: parentCommentId
        })

      if (error) {
        console.error('Book reply insert error:', error)
        setError(`Failed to post reply: ${error.message || 'Please try again.'}`)
      } else {
        setReplyText("")
        setReplyingTo(null)
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg p-${isCompact ? '4' : '6'} shadow-sm`}>
        <h3 className={`${isCompact ? 'text-base' : 'text-lg'} font-serif mb-4 text-gray-800`}>Comments</h3>
        <div className="text-gray-500 text-sm">Loading comments...</div>
      </div>
    )
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-${isCompact ? '4' : '6'} shadow-sm`}>
      <h3 className={`${isCompact ? 'text-base' : 'text-lg'} font-serif mb-4 text-blue-800`}>
        💬 Comments ({comments.reduce((total, comment) => total + 1 + (comment.replies?.length || 0), 0)})
      </h3>

      {/* Comment Form */}
      {canComment && userId ? (
        <form onSubmit={handleSubmitComment} className="mb-6">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Share your thoughts about this book..."
            className={`w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent resize-none bg-white text-gray-900 placeholder-gray-500 ${isCompact ? 'text-sm' : ''}`}
            rows={isCompact ? 2 : 3}
            maxLength={1000}
          />
          
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-gray-500">
              {newComment.length}/1000 characters
            </span>
            
            {error && (
              <span className="text-red-600 text-sm">{error}</span>
            )}
            
            <button
              type="submit"
              disabled={submitting || !newComment.trim()}
              className={`bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${isCompact ? 'text-sm' : ''}`}
            >
              {submitting ? "Posting..." : "Post Comment"}
            </button>
          </div>
        </form>
      ) : !canComment ? (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-gray-600 text-sm">
            Sign in to join the conversation about this book
          </p>
        </div>
      ) : (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-gray-600 text-sm">
            Please sign in to comment
          </p>
        </div>
      )}

      {/* Comments List */}
      {comments.length > 0 ? (
        <div className={`space-y-${isCompact ? '3' : '6'} ${isCompact ? 'max-h-96 overflow-y-auto' : ''}`}>
          {comments.map((comment) => (
            <NestedComment
              key={comment.id}
              comment={comment}
              canComment={canComment}
              userId={userId}
              onReply={handleSubmitReply}
              replyingTo={replyingTo}
              setReplyingTo={setReplyingTo}
              replyText={replyText}
              setReplyText={setReplyText}
              submitting={submitting}
              maxDepth={maxDepth}
              isCompact={isCompact}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 text-sm">
            No comments yet. Be the first to share your thoughts about this book!
          </p>
        </div>
      )}
    </div>
  )
}
