import { useState, useEffect, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { photoStorage } from "@/lib/supabase/storage"
import { validateImageFile } from "@/lib/watermark"
import Image from "next/image"

interface VideoUploadProps {
  postId?: string
  onVideoUploaded?: (videoId: string) => void
  onCreateEntry?: () => Promise<string | null>
  hasPhotos?: boolean
  onMediaTypeChange?: (hasVideo: boolean) => void
  zenMode?: boolean
}

export function VideoUpload({ postId, onVideoUploaded, onCreateEntry, hasPhotos, onMediaTypeChange, zenMode = false }: VideoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [uploadedVideo, setUploadedVideo] = useState<{
    id: string
    r2PublicUrl: string
    title: string
    customThumbnailUrl?: string
  } | null>(null)
  const [processing, setProcessing] = useState(false)
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [customThumbnail, setCustomThumbnail] = useState<string | null>(null)
  const [thumbnailUploading, setThumbnailUploading] = useState(false)
  const [thumbnailError, setThumbnailError] = useState<string | null>(null)
  const thumbnailInputRef = useRef<HTMLInputElement>(null)
  const supabase = createSupabaseClient()

  // Load existing video when editing
  useEffect(() => {
    if (postId) {
      loadExistingVideo()
    }
  }, [postId])

  const loadExistingVideo = async () => {
    try {
      const { data: videos, error } = await supabase
        .from('videos')
        .select('id, r2_public_url, title, custom_thumbnail_url')
        .eq('post_id', postId)
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) {
        console.error('Error loading existing video:', error)
        return
      }

      if (videos && videos.length > 0) {
        const video = videos[0]
        setUploadedVideo({
          id: video.id,
          r2PublicUrl: video.r2_public_url,
          title: video.title || 'Uploaded video',
          customThumbnailUrl: video.custom_thumbnail_url
        })
        if (video.custom_thumbnail_url) {
          setCustomThumbnail(video.custom_thumbnail_url)
        }
        setUploadSuccess(true)
        onMediaTypeChange?.(true)
        console.log('Loaded existing video:', video)
      }
    } catch (error) {
      console.error('Failed to load existing video:', error)
    }
  }







  const handleThumbnailUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !uploadedVideo) return

    setThumbnailUploading(true)
    setThumbnailError(null)

    try {
      // Validate image file
      const validation = validateImageFile(file)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // Check aspect ratio - should be 1:1 (square)
      const img = document.createElement('img')
      img.onload = async () => {
        try {
          const aspectRatio = img.width / img.height
          if (Math.abs(aspectRatio - 1) > 0.1) { // Allow small tolerance
            throw new Error('Thumbnail must be square (1:1 aspect ratio). Please crop your image to be square.')
          }

          // Create unique filename for thumbnail
          const fileExt = file.name.split('.').pop()
          const fileName = `video-thumbnails/${uploadedVideo.id}/${Date.now()}-thumbnail.${fileExt}`

          // Upload to photo storage
          const { error: uploadError } = await photoStorage.upload(fileName, file)
          if (uploadError) {
            throw new Error(`Failed to upload thumbnail: ${uploadError.message}`)
          }

          // Get public URL
          const { data: { publicUrl } } = photoStorage.getPublicUrl(fileName)

          // Update video record with custom thumbnail
          const { error: updateError } = await supabase
            .from('videos')
            .update({ custom_thumbnail_url: publicUrl })
            .eq('id', uploadedVideo.id)

          if (updateError) {
            throw new Error('Failed to save thumbnail')
          }

          setCustomThumbnail(publicUrl)
          setUploadedVideo(prev => prev ? { ...prev, customThumbnailUrl: publicUrl } : null)

        } catch (error: any) {
          setThumbnailError(error.message)
        } finally {
          setThumbnailUploading(false)
        }
      }

      img.onerror = () => {
        setThumbnailError('Invalid image file')
        setThumbnailUploading(false)
      }

      img.src = URL.createObjectURL(file)

    } catch (error: any) {
      setThumbnailError(error.message)
      setThumbnailUploading(false)
    }

    // Clear the input
    event.target.value = ''
  }

  const handleVideoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Check if there's already a video and confirm replacement
    if (uploadedVideo) {
      const confirmReplace = confirm('This post already has a video. Do you want to replace it?')
      if (!confirmReplace) {
        event.target.value = '' // Clear the file input
        return
      }
    }

    // Create local preview immediately
    const previewUrl = URL.createObjectURL(file)
    setLocalPreviewUrl(previewUrl)

    // Validate file type
    if (!file.type.startsWith('video/')) {
      setError('Please select a valid video file')
      return
    }

    // Validate file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      setError('Video file must be less than 100MB')
      return
    }

    console.log('🎥 Starting video upload:', file.name)
    setUploading(true)
    setError(null)
    setUploadProgress(10) // Show immediate progress

    try {
      let currentPostId = postId

      // If no entry exists, create one with current content
      if (!currentPostId && onCreateEntry) {
        const savedEntryId = await onCreateEntry()
        if (!savedEntryId) {
          throw new Error('Failed to save entry for video upload')
        }
        currentPostId = savedEntryId
      } else if (!currentPostId) {
        throw new Error('Unable to save entry for video upload')
      }

      // Delete any existing videos for this entry first
      if (currentPostId) {
        await supabase
          .from('videos')
          .delete()
          .eq('post_id', currentPostId)
      }

      // Step 1: Get presigned upload URL from R2
      const createResponse = await fetch('/api/r2/upload-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filename: file.name,
          postId: currentPostId
        })
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(errorData.error || 'Failed to create video')
      }

      const { uploadUrl, publicUrl, video } = await createResponse.json()

      // Step 2: Upload video file directly to R2
      setUploadProgress(50) // Show progress

      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload video file')
      }

      // Step 4: Verify upload by checking if video is accessible
      setUploadProgress(90)

      try {
        // Test if the video is accessible via public URL
        const testResponse = await fetch(publicUrl, { method: 'HEAD' })
        if (testResponse.ok) {
          console.log('✅ Video upload verified - file is accessible')
        } else {
          console.warn('⚠️ Video may not be immediately accessible, but upload likely succeeded')
        }
      } catch (testError) {
        console.warn('⚠️ Could not verify video accessibility (CORS), but upload likely succeeded')
      }

      setUploadProgress(100)

      // Set uploaded video for preview
      const videoData = {
        id: video.id,
        r2PublicUrl: publicUrl,
        title: file.name.replace(/\.[^/.]+$/, "")
      }

      console.log('🎥 Video upload completed:', videoData)
      setUploadedVideo(videoData)
      setUploadSuccess(true)
      setProcessing(false) // Don't show processing - video is ready to use
      onVideoUploaded?.(video.id)
      onMediaTypeChange?.(true)

      // Reset form
      event.target.value = ''
      
    } catch (err) {
      console.error('Video upload error:', err)
      setError(err instanceof Error ? err.message : 'Upload failed')
    } finally {
      setUploading(false)
      setTimeout(() => setUploadProgress(0), 2000)
    }
  }

  return (
    <div className={`border-t p-6 ${
      zenMode ? 'border-slate-700/30' : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-lg font-medium ${
          zenMode ? 'text-white/90' : 'text-gray-900'
        }`}>Videos</h3>
        {uploadedVideo && (
          <span className={`text-sm ${
            zenMode ? 'text-white/60' : 'text-gray-500'
          }`}>
            {processing ? "Processing..." : "Ready"}
          </span>
        )}
      </div>

      {/* Upload Button */}
      <div className="mb-4">
        <input
          type="file"
          accept="video/*"
          onChange={handleVideoUpload}
          disabled={uploading || processing || hasPhotos}
          className="hidden"
          id="video-upload"
        />
        <button
          onClick={() => document.getElementById('video-upload')?.click()}
          disabled={uploading || processing || hasPhotos}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {uploading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Uploading...</span>
            </div>
          ) : processing ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Processing...</span>
            </div>
          ) : hasPhotos ? (
            "Photos already added"
          ) : uploadedVideo ? (
            "Replace Video"
          ) : (
            "Add Video"
          )}
        </button>
      </div>

      {/* Upload Progress */}
      {uploading && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm font-medium mb-3">
            📤 Uploading video...
          </p>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-xs text-blue-500 mt-2">{uploadProgress}% complete</p>
        </div>
      )}

      {/* Success Message */}
      {uploadSuccess && !uploading && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-600 text-sm font-medium mb-2">
            ✅ Video uploaded successfully!
          </p>
          <p className="text-xs text-green-500">
            Your video is ready to view and your post can be published.
          </p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Video Preview */}
      {(localPreviewUrl || uploadedVideo) && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Video Preview</h4>
          <div className="bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
            {localPreviewUrl ? (
              // Show local preview
              <video
                controls
                className="w-full h-full"
                style={{ aspectRatio: '16/9' }}
                src={localPreviewUrl}
              />
            ) : uploadedVideo ? (
              // Show R2 video when uploaded
              <video
                controls
                className="w-full h-full"
                style={{ aspectRatio: '16/9' }}
                crossOrigin="anonymous"
                onError={(e) => {
                  console.error('Video preview error:', e);
                  console.error('Video URL:', uploadedVideo.r2PublicUrl);
                }}
                onLoadStart={() => console.log('Video preview load started:', uploadedVideo.r2PublicUrl)}
                onCanPlay={() => console.log('Video preview can play:', uploadedVideo.r2PublicUrl)}
              >
                <source src={uploadedVideo.r2PublicUrl} type="video/mp4" />
                <p className="text-white p-4">Video uploaded successfully!</p>
              </video>
            ) : null}
          </div>
          <div className="mt-2 flex items-center justify-between text-sm text-gray-500">
            <span>{uploadedVideo?.title ?? 'Selected video'}</span>
            <button
              onClick={() => {
                setUploadedVideo(null)
                setProcessing(false)
                setUploadSuccess(false)
                setLocalPreviewUrl(null)
                if (localPreviewUrl) {
                  URL.revokeObjectURL(localPreviewUrl)
                }
              }}
              className="text-red-600 hover:text-red-700"
            >
              Remove
            </button>
          </div>
        </div>
      )}

      {/* Custom Thumbnail Upload */}
      {uploadedVideo && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Custom Video Thumbnail (Optional)</h4>
          <p className="text-xs text-gray-600 mb-3">
            Upload a custom 1:1 square thumbnail for your video. This will be shown in profile cards instead of the auto-generated thumbnail.
          </p>

          {thumbnailError && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-600 text-xs">
              {thumbnailError}
            </div>
          )}

          <div className="flex items-start gap-4">
            {/* Current Thumbnail Preview */}
            {(customThumbnail || uploadedVideo.customThumbnailUrl) && (
              <div className="flex-shrink-0">
                <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden border border-gray-300">
                  <Image
                    src={customThumbnail || uploadedVideo.customThumbnailUrl || ''}
                    alt="Custom thumbnail"
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1 text-center">Current</p>
              </div>
            )}

            {/* Upload Controls */}
            <div className="flex-1">
              <input
                ref={thumbnailInputRef}
                type="file"
                accept="image/*"
                onChange={handleThumbnailUpload}
                className="hidden"
              />
              <button
                onClick={() => thumbnailInputRef.current?.click()}
                disabled={thumbnailUploading}
                className="bg-blue-100 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {thumbnailUploading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>Uploading...</span>
                  </div>
                ) : (
                  <>
                    {customThumbnail || uploadedVideo.customThumbnailUrl ? 'Change' : 'Upload'} Thumbnail
                  </>
                )}
              </button>
              <p className="text-xs text-gray-500 mt-1">
                Square images only (1:1 ratio) • Max 10MB • JPG, PNG
              </p>
            </div>
          </div>
        </div>
      )}

      <p className="text-xs text-gray-500">
        Supported formats: MP4, MOV, AVI • Max size: 100MB
      </p>
    </div>
  )
}
