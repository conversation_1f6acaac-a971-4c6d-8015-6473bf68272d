"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"

interface PriceEditorProps {
  initialPrice?: number
}

export function PriceEditor({ initialPrice }: PriceEditorProps) {
  const [price, setPrice] = useState(initialPrice || 999)
  const [displayValue, setDisplayValue] = useState(initialPrice ? (initialPrice / 100).toFixed(2) : "")
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Convert cents to dollars for display
  const priceInDollars = initialPrice ? (price / 100).toFixed(2) : "Not set"

  const handleSave = async () => {
    // Validate on save only
    if (price < 299) {
      setError("Minimum price is $2.99")
      return
    }
    if (price > 5000) {
      setError("Maximum price is $50.00")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")

    try {
      const response = await fetch('/api/profile/pricing', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price_monthly: price
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess("Price updated successfully!")
        setIsEditing(false)

        // Refresh the page to show updated price after a short delay
        setTimeout(() => {
          window.location.reload()
        }, 1500)
      } else {
        setError(data.error || "Failed to update price")
      }
    } catch (err) {
      setError("Failed to update price. Please try again.")
      console.error('Price update error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setPrice(initialPrice || 999)
    setDisplayValue(initialPrice ? (initialPrice / 100).toFixed(2) : "")
    setIsEditing(false)
    setError("")
    setSuccess("")
  }

  const handlePriceChange = (value: string) => {
    setDisplayValue(value)

    const dollars = parseFloat(value)
    if (!isNaN(dollars) && dollars > 0) {
      const cents = Math.round(dollars * 100)
      setPrice(cents)
      setError("")
    }
  }

  if (!isEditing) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 space-y-3 w-full">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <label className="text-sm font-medium text-gray-600 mb-1 block">Monthly Subscription Price</label>
            <div className="flex items-baseline gap-1">
              {initialPrice ? (
                <>
                  <span className="text-2xl font-bold text-gray-900">${priceInDollars}</span>
                  <span className="text-sm text-gray-500">per month</span>
                </>
              ) : (
                <span className="text-lg text-gray-500 italic">No price set</span>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              You keep 80% after platform fees
            </p>
          </div>
          <Button
            onClick={() => setIsEditing(true)}
            variant="outline"
            size="sm"
            className="shrink-0 px-3 py-1 text-xs"
          >
            Edit
          </Button>
        </div>

        {success && (
          <div className="text-green-700 text-sm font-medium bg-green-100 p-3 rounded-md border border-green-200">
            ✓ {success}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200 space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Set Your Monthly Subscription Price
        </label>

        <div className="flex items-center gap-2 mb-2 w-full max-w-full">
          <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent flex-1 min-w-0">
            <span className="text-gray-500 pl-3 pr-1 text-lg font-medium">$</span>
            <input
              type="text"
              inputMode="decimal"
              value={displayValue}
              onChange={(e) => handlePriceChange(e.target.value)}
              className="w-full max-w-full px-2 py-3 border-0 rounded-r-lg focus:outline-none text-lg font-medium min-w-0"
              placeholder="9.99"
              autoFocus
            />
          </div>
          <span className="text-sm text-gray-600 shrink-0">per month</span>
        </div>

        <div className="text-xs text-gray-600 space-y-1">
          <p>• You keep 80% after platform fees</p>
          <p>• Changes apply to new subscribers immediately</p>
        </div>
      </div>

      {error && (
        <div className="text-red-700 text-sm font-medium bg-red-100 p-3 rounded-md border border-red-200">
          ⚠ {error}
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-2">
        <Button
          onClick={handleSave}
          disabled={loading}
          className="flex-1 bg-blue-600 hover:bg-blue-700"
        >
          {loading ? (
            <div className="flex items-center justify-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Saving...
            </div>
          ) : (
            'Save Price'
          )}
        </Button>
        <Button
          onClick={handleCancel}
          variant="outline"
          disabled={loading}
          className="sm:w-auto"
        >
          Cancel
        </Button>
      </div>
    </div>
  )
}
