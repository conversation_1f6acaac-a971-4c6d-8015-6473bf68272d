import { createSupabaseServerClient } from "@/lib/supabase/client"
import { redirect } from "next/navigation"
import Link from "next/link"
import Image from "next/image"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export default async function SubscriptionsPage() {
  const supabase = await createSupabaseServerClient()
  
  // Check if user is authenticated
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  
  if (authError || !user) {
    redirect('/login')
  }

  // Get user profile
  const { data: profile, error: profileError } = await supabase
    .from("users")
    .select("*")
    .eq("id", user.id)
    .single()

  if (profileError || !profile) {
    redirect('/')
  }

  // Get user's active subscriptions with writer details
  const { data: subscriptions } = await supabase
    .from("subscriptions")
    .select(`
      id,
      current_period_end,
      created_at,
      writer:users!writer_id (
        id,
        name,
        avatar,
        bio,
        price_monthly
      )
    `)
    .eq("reader_id", user.id)
    .eq("status", "active")
    .gte("current_period_end", new Date().toISOString())
    .order("created_at", { ascending: false })

  // Get recent entries from subscribed writers
  const writerIds = subscriptions?.map(sub => (sub.writer as any).id) || [] // eslint-disable-line @typescript-eslint/no-explicit-any
  
  let recentEntries: any[] = [] // eslint-disable-line @typescript-eslint/no-explicit-any
  if (writerIds.length > 0) {
    const { data: entries } = await supabase
      .from("diary_entries")
      .select(`
        id,
        title,
        body_md,
        created_at,
        is_free,
        user:users!user_id (
          id,
          name,
          avatar
        )
      `)
      .in("user_id", writerIds)
      .eq("is_hidden", false)
      .order("created_at", { ascending: false })
      .limit(10)

    recentEntries = entries || []
  }

  // Get payment history
  const { data: payments } = await supabase
    .from("payments")
    .select(`
      id,
      amount_cents,
      created_at,
      kind,
      writer:users!writer_id (
        id,
        name
      )
    `)
    .eq("payer_id", user.id)
    .order("created_at", { ascending: false })
    .limit(10)

  return (
    <div className="min-h-screen bg-[#F7F7F7] pt-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">My Subscriptions</h1>
          <p className="text-gray-600 font-serif">
            Manage your subscriptions and discover new content
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Active Subscriptions</h3>
            <p className="text-2xl font-bold text-gray-900">{subscriptions?.length || 0}</p>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Monthly Spend</h3>
            <p className="text-2xl font-bold text-gray-900">
              {formatPrice(subscriptions?.reduce((total, sub) => total + ((sub.writer as any).price_monthly || 0), 0) || 0)} {/* eslint-disable-line @typescript-eslint/no-explicit-any */}
            </p>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Writers Following</h3>
            <p className="text-2xl font-bold text-gray-900">{subscriptions?.length || 0}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Active Subscriptions */}
          <div className="space-y-6">
            <h2 className="text-xl font-serif text-gray-800">Active Subscriptions</h2>
            
            {subscriptions && subscriptions.length > 0 ? (
              <div className="space-y-4">
                {subscriptions.map((subscription) => {
                  const writer = subscription.writer as any // eslint-disable-line @typescript-eslint/no-explicit-any
                  return (
                    <div key={subscription.id} className="bg-white rounded-lg p-6 shadow-sm">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {writer.avatar ? (
                            <Image 
                              src={writer.avatar} 
                              alt={writer.name || 'Writer avatar'}
                              width={48}
                              height={48}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-lg font-serif text-gray-500">
                              {(writer.name || 'A').charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-1">
                            {writer.name || 'Anonymous Writer'}
                          </h3>
                          {writer.bio && (
                            <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                              {writer.bio}
                            </p>
                          )}
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">
                              {formatPrice(writer.price_monthly)}/month
                            </span>
                            <span className="text-gray-500">
                              Renews {formatDate(subscription.current_period_end)}
                            </span>
                          </div>
                        </div>
                        
                        <Link
                          href={`/u/${writer.id}`}
                          className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                        >
                          View Profile
                        </Link>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="bg-white rounded-lg p-8 shadow-sm text-center">
                <p className="text-gray-600 font-serif mb-4">
                  You don&apos;t have any active subscriptions yet.
                </p>
                <Link
                  href="/"
                  className="inline-block bg-gray-800 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                  Discover Writers
                </Link>
              </div>
            )}
          </div>

          {/* Recent Entries */}
          <div className="space-y-6">
            <h2 className="text-xl font-serif text-gray-800">Recent Entries</h2>
            
            {recentEntries.length > 0 ? (
              <div className="space-y-4">
                {recentEntries.slice(0, 5).map((entry) => {
                  const author = entry.user as any // eslint-disable-line @typescript-eslint/no-explicit-any
                  return (
                    <div key={entry.id} className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {author.avatar ? (
                            <Image 
                              src={author.avatar} 
                              alt={author.name || 'Author avatar'}
                              width={32}
                              height={32}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-sm font-serif text-gray-500">
                              {(author.name || 'A').charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-gray-900 truncate text-sm">
                              {entry.title}
                            </h3>
                            {entry.is_free && (
                              <span className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded">
                                FREE
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-gray-600 text-xs mb-2">
                            <span>by {author.name}</span>
                            {author.has_day1_badge && (
                              <Day1Badge
                                signupNumber={author.signup_number}
                                badgeTier={author.badge_tier}
                                size="sm"
                                className="flex-shrink-0"
                              />
                            )}
                            <span>• {formatDate(entry.created_at)}</span>
                          </div>
                          <p className="text-gray-700 text-sm line-clamp-2">
                            {entry.body_md.slice(0, 100)}...
                          </p>
                        </div>
                        
                        <Link
                          href={`/d/${entry.id}`}
                          className="text-gray-600 hover:text-gray-800 text-xs font-medium whitespace-nowrap"
                        >
                          Read
                        </Link>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="bg-white rounded-lg p-6 shadow-sm text-center">
                <p className="text-gray-600 font-serif text-sm">
                  No recent entries from your subscribed writers.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Payment History */}
        {payments && payments.length > 0 && (
          <div className="mt-8">
            <h2 className="text-xl font-serif text-gray-800 mb-6">Payment History</h2>
            
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="divide-y divide-gray-200">
                {payments.map((payment) => {
                  const writer = payment.writer as any // eslint-disable-line @typescript-eslint/no-explicit-any
                  return (
                    <div key={payment.id} className="p-4 flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">
                          {payment.kind === 'sub' ? 'Subscription' : 'Donation'} - {writer.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(payment.created_at)}
                        </p>
                      </div>
                      <span className="font-medium text-gray-900">
                        {formatPrice(payment.amount_cents)}
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}