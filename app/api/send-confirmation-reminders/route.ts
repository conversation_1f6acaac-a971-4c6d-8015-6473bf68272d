import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { sendEmail, EmailTemplates } from '@/lib/email/resend'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get the current user to verify admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin (you can adjust this based on your admin system)
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || userProfile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get request parameters
    const { 
      dryRun = false, 
      maxEmails = 50,
      minHoursSinceLastReminder = 24 
    } = await request.json()

    // Get unconfirmed users who haven't received a reminder recently
    const cutoffDate = new Date()
    cutoffDate.setHours(cutoffDate.getHours() - minHoursSinceLastReminder)

    // First, get all unconfirmed users
    const { data: unconfirmedUsers, error: usersError } = await supabase
      .from('auth.users')
      .select('id, email, raw_user_meta_data, created_at, confirmation_sent_at')
      .is('email_confirmed_at', null)
      .order('created_at', { ascending: false })
      .limit(maxEmails * 2) // Get more than we need to filter

    if (usersError) {
      console.error('Error fetching unconfirmed users:', usersError)
      return NextResponse.json(
        { error: 'Failed to fetch unconfirmed users' },
        { status: 500 }
      )
    }

    if (!unconfirmedUsers || unconfirmedUsers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unconfirmed users found',
        emailsSent: 0,
        usersProcessed: 0
      })
    }

    // Filter out users who have received a reminder recently
    const usersToEmail = []
    
    for (const user of unconfirmedUsers) {
      if (usersToEmail.length >= maxEmails) break

      // Check if this user has received a confirmation reminder recently
      const { data: recentEmails, error: emailCheckError } = await supabase
        .from('email_logs')
        .select('created_at')
        .eq('recipient_email', user.email)
        .eq('email_type', 'confirmation-reminder')
        .gte('created_at', cutoffDate.toISOString())
        .limit(1)

      if (emailCheckError) {
        console.error('Error checking recent emails for', user.email, emailCheckError)
        continue
      }

      // Skip if they've received a reminder recently
      if (recentEmails && recentEmails.length > 0) {
        console.log(`Skipping ${user.email} - received reminder recently`)
        continue
      }

      usersToEmail.push(user)
    }

    console.log(`Found ${usersToEmail.length} users to send reminders to`)

    if (dryRun) {
      return NextResponse.json({
        success: true,
        message: 'Dry run completed',
        emailsSent: 0,
        usersProcessed: usersToEmail.length,
        users: usersToEmail.map(u => ({
          email: u.email,
          name: u.raw_user_meta_data?.name || 'User',
          createdAt: u.created_at
        }))
      })
    }

    // Send reminder emails
    let emailsSent = 0
    let emailsFailed = 0
    const results = []

    for (const user of usersToEmail) {
      try {
        const userName = user.raw_user_meta_data?.name || 'User'
        const template = EmailTemplates.confirmationReminder(userName)
        
        const emailResult = await sendEmail({
          to: user.email,
          subject: template.subject,
          html: template.html,
          text: template.text,
          tag: template.tag
        })

        if (emailResult.success) {
          // Log the email send
          await supabase.from('email_logs').insert({
            user_id: user.id,
            email_type: 'confirmation-reminder',
            recipient_email: user.email,
            status: 'sent',
            resend_message_id: emailResult.id,
            metadata: {
              userName,
              dryRun: false,
              apiTriggered: true
            }
          })

          emailsSent++
          results.push({
            email: user.email,
            status: 'sent',
            messageId: emailResult.id
          })
          
          console.log(`✅ Sent reminder to ${user.email}`)
        } else {
          // Log the failure
          await supabase.from('email_logs').insert({
            user_id: user.id,
            email_type: 'confirmation-reminder',
            recipient_email: user.email,
            status: 'failed',
            error_message: emailResult.error,
            metadata: {
              userName,
              dryRun: false,
              apiTriggered: true
            }
          })

          emailsFailed++
          results.push({
            email: user.email,
            status: 'failed',
            error: emailResult.error
          })
          
          console.error(`❌ Failed to send reminder to ${user.email}:`, emailResult.error)
        }

        // Add a small delay to be respectful to the email service
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`Error processing ${user.email}:`, error)
        emailsFailed++
        results.push({
          email: user.email,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sent ${emailsSent} confirmation reminders`,
      emailsSent,
      emailsFailed,
      usersProcessed: usersToEmail.length,
      results: results.slice(0, 10) // Only return first 10 results to avoid large responses
    })

  } catch (error) {
    console.error('Error in send-confirmation-reminders:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to check status and get preview
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get the current user to verify admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || userProfile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get count of unconfirmed users
    const { count: unconfirmedCount, error: countError } = await supabase
      .from('auth.users')
      .select('*', { count: 'exact', head: true })
      .is('email_confirmed_at', null)

    if (countError) {
      console.error('Error counting unconfirmed users:', countError)
      return NextResponse.json(
        { error: 'Failed to count unconfirmed users' },
        { status: 500 }
      )
    }

    // Get recent reminder email stats
    const oneDayAgo = new Date()
    oneDayAgo.setHours(oneDayAgo.getHours() - 24)

    const { count: recentReminders, error: remindersError } = await supabase
      .from('email_logs')
      .select('*', { count: 'exact', head: true })
      .eq('email_type', 'confirmation-reminder')
      .gte('created_at', oneDayAgo.toISOString())

    if (remindersError) {
      console.error('Error counting recent reminders:', remindersError)
    }

    return NextResponse.json({
      unconfirmedUsers: unconfirmedCount || 0,
      recentReminders: recentReminders || 0,
      lastRemindersSent: oneDayAgo.toISOString()
    })

  } catch (error) {
    console.error('Error in GET send-confirmation-reminders:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
