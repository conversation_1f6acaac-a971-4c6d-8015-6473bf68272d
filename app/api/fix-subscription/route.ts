/**
 * Emergency subscription fix endpoint
 * Creates subscription record for recent payment
 */

import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST(request: NextRequest) {
  try {
    const { payerId, writerId } = await request.json()
    
    if (!payerId || !writerId) {
      return NextResponse.json(
        { error: "Missing payerId or writerId" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()

    // Check if subscription already exists
    const { data: existingSubscription } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("reader_id", payerId)
      .eq("writer_id", writerId)
      .single()

    if (existingSubscription) {
      return NextResponse.json({
        message: "Subscription already exists",
        subscriptionId: existingSubscription.id
      })
    }

    // Create the subscription record
    const activeUntil = new Date()
    activeUntil.setMonth(activeUntil.getMonth() + 1)

    const { data: newSubscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .insert({
        reader_id: payerId,
        writer_id: writerId,
        status: 'active',
        current_period_start: new Date().toISOString(),
        current_period_end: activeUntil.toISOString(),
        stripe_subscription_id: `manual_fix_${Date.now()}`
      })
      .select()
      .single()

    if (subscriptionError) {
      console.error("Failed to create subscription:", subscriptionError)
      return NextResponse.json(
        { error: "Failed to create subscription", details: subscriptionError },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Subscription created successfully",
      subscription: newSubscription
    })

  } catch (error) {
    console.error("Error in fix-subscription:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET endpoint to check subscription status
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const payerId = searchParams.get('payerId')
  const writerId = searchParams.get('writerId')

  if (!payerId || !writerId) {
    return NextResponse.json(
      { error: "Missing payerId or writerId parameters" },
      { status: 400 }
    )
  }

  const supabase = await createSupabaseServerClient()

  const { data: subscription, error } = await supabase
    .from("subscriptions")
    .select("*")
    .eq("reader_id", payerId)
    .eq("writer_id", writerId)
    .single()

  if (error) {
    return NextResponse.json({
      hasSubscription: false,
      error: error.message
    })
  }

  return NextResponse.json({
    hasSubscription: true,
    subscription
  })
}
