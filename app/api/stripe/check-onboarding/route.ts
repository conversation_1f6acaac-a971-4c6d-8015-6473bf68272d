import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { stripe } from '@/lib/stripe'

export async function POST() {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('stripe_account_id, stripe_onboarding_complete')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || !profile.stripe_account_id) {
      return NextResponse.json({ error: 'No Stripe account found' }, { status: 404 })
    }

    // Check account status with Stripe
    const account = await stripe.accounts.retrieve(profile.stripe_account_id)
    
    const isComplete = account.details_submitted && 
                      account.charges_enabled && 
                      account.payouts_enabled

    console.log('Manual onboarding check:', {
      accountId: account.id,
      userId: user.id,
      detailsSubmitted: account.details_submitted,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      isComplete,
      currentDbStatus: profile.stripe_onboarding_complete
    })

    // Update database if status has changed
    if (profile.stripe_onboarding_complete !== isComplete) {
      // Prepare update data
      const updateData: any = {
        stripe_onboarding_complete: isComplete
      }

      // If onboarding is now complete and user doesn't have a price set, set default $9.99
      if (isComplete) {
        const { data: currentProfile } = await supabase
          .from('users')
          .select('price_monthly')
          .eq('id', user.id)
          .single()

        if (currentProfile && !currentProfile.price_monthly) {
          updateData.price_monthly = 999 // Default $9.99 in cents
          console.log('Setting default price of $9.99 for newly onboarded user (manual check)')
        }
      }

      const { error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', user.id)

      if (updateError) {
        console.error('Error updating onboarding status:', updateError)
        return NextResponse.json({ error: 'Failed to update status' }, { status: 500 })
      }

      console.log('Onboarding status updated:', { userId: user.id, isComplete })
    }

    return NextResponse.json({ 
      success: true,
      isComplete,
      updated: profile.stripe_onboarding_complete !== isComplete
    })

  } catch (error) {
    console.error('Check onboarding error:', error)
    return NextResponse.json(
      { error: 'Failed to check onboarding status' },
      { status: 500 }
    )
  }
}
