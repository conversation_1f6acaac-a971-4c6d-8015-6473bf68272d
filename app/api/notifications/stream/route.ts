/**
 * Server-Sent Events endpoint for real-time notifications
 * Handles payment and message notifications for OnlyDiary
 */

import { NextRequest } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClient()
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  
  if (authError || !user) {
    return new Response('Unauthorized', { status: 401 })
  }

  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const data = `data: ${JSON.stringify({ type: 'connected', userId: user.id })}\n\n`
      controller.enqueue(new TextEncoder().encode(data))

      // Set up Supabase real-time subscription for payments
      const paymentsChannel = supabase
        .channel('payments')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'payments',
            filter: `writer_id=eq.${user.id}`
          },
          (payload) => {
            console.log('💰 Payment received for user:', user.id)
            const notification = {
              type: 'payment',
              data: {
                amount: payload.new.amount_cents,
                payerId: payload.new.payer_id,
                timestamp: new Date().toISOString()
              }
            }
            
            const message = `data: ${JSON.stringify(notification)}\n\n`
            controller.enqueue(new TextEncoder().encode(message))
          }
        )
        .subscribe()

      // Set up subscription for new subscriptions
      const subscriptionsChannel = supabase
        .channel('subscriptions')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'subscriptions',
            filter: `writer_id=eq.${user.id}`
          },
          (payload) => {
            console.log('📝 New subscription for user:', user.id)
            const notification = {
              type: 'subscription',
              data: {
                subscriberId: payload.new.reader_id,
                timestamp: new Date().toISOString()
              }
            }
            
            const message = `data: ${JSON.stringify(notification)}\n\n`
            controller.enqueue(new TextEncoder().encode(message))
          }
        )
        .subscribe()

      // Set up subscription for messages (when you implement messaging)
      const messagesChannel = supabase
        .channel('messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages', // You'll need to create this table
            filter: `recipient_id=eq.${user.id}`
          },
          (payload) => {
            console.log('💬 New message for user:', user.id)
            const notification = {
              type: 'message',
              data: {
                senderId: payload.new.sender_id,
                timestamp: new Date().toISOString()
              }
            }
            
            const message = `data: ${JSON.stringify(notification)}\n\n`
            controller.enqueue(new TextEncoder().encode(message))
          }
        )
        .subscribe()

      // Keep connection alive with heartbeat
      const heartbeat = setInterval(() => {
        const heartbeatMessage = `data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() })}\n\n`
        controller.enqueue(new TextEncoder().encode(heartbeatMessage))
      }, 30000) // Every 30 seconds

      // Cleanup function
      const cleanup = () => {
        clearInterval(heartbeat)
        paymentsChannel.unsubscribe()
        subscriptionsChannel.unsubscribe()
        messagesChannel.unsubscribe()
        controller.close()
      }

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup)
      
      // Store cleanup function for potential manual cleanup
      ;(controller as any).cleanup = cleanup
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  })
}
