-- Quick check for subscription creation issues

-- 1. Verify the specific users involved in the failed subscription
SELECT 
    'Reader' as user_type,
    id,
    name,
    email,
    role,
    created_at
FROM users 
WHERE id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d'

UNION ALL

SELECT 
    'Writer' as user_type,
    id,
    name,
    email,
    role,
    created_at
FROM users 
WHERE id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 2. Check writer's subscription setup
SELECT 
    id,
    name,
    price_monthly,
    stripe_account_id,
    stripe_onboarding_complete,
    CASE 
        WHEN price_monthly IS NULL OR price_monthly = 0 THEN 'No pricing set'
        WHEN stripe_account_id IS NULL THEN 'No Stripe account'
        WHEN stripe_onboarding_complete = false THEN 'Stripe onboarding incomplete'
        ELSE 'Ready for subscriptions'
    END as subscription_status
FROM users 
WHERE id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 3. Check if writer has any paid content
SELECT 
    writer_id,
    COUNT(*) as total_entries,
    COUNT(CASE WHEN is_free = false THEN 1 END) as paid_entries,
    COUNT(CASE WHEN is_free = true THEN 1 END) as free_entries,
    CASE 
        WHEN COUNT(CASE WHEN is_free = false THEN 1 END) > 0 THEN 'Has paid content'
        ELSE 'Only free content'
    END as content_status
FROM diary_entries 
WHERE writer_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
AND is_published = true
GROUP BY writer_id;

-- 4. Check existing relationship between these users
SELECT 
    'Follow' as relationship_type,
    follower_id as user1,
    writer_id as user2,
    created_at
FROM follows 
WHERE follower_id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d' 
AND writer_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'

UNION ALL

SELECT 
    'Subscription' as relationship_type,
    reader_id as user1,
    writer_id as user2,
    created_at
FROM subscriptions 
WHERE reader_id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d' 
AND writer_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 5. Test if we can insert a subscription manually (to check constraints)
-- This will show any constraint violations
BEGIN;

INSERT INTO subscriptions (reader_id, writer_id, status, current_period_start, current_period_end)
VALUES (
    'f296c9e6-f15d-402e-ab58-eebdc1947e1d',
    'aaa7e8be-277c-459e-87aa-c4b736cc143e',
    'active',
    NOW(),
    NOW() + INTERVAL '30 days'
);

-- Don't commit, just test
ROLLBACK;

-- 6. Check RLS policies that might be blocking access
SELECT 
    policyname,
    cmd,
    qual,
    with_check,
    CASE 
        WHEN cmd = 'ALL' THEN 'Affects all operations'
        WHEN cmd = 'SELECT' THEN 'Affects reading'
        WHEN cmd = 'INSERT' THEN 'Affects creation'
        WHEN cmd = 'UPDATE' THEN 'Affects updates'
        WHEN cmd = 'DELETE' THEN 'Affects deletion'
    END as policy_scope
FROM pg_policies 
WHERE tablename = 'subscriptions'
ORDER BY cmd;
