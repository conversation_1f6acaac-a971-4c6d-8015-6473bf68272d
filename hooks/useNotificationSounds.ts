/**
 * React hook for OnlyDiary notification sounds
 */

import { useEffect, useCallback } from 'react'
import { SoundManager, playPaymentSound, playMessageSound, playCreationSound, initializeSounds } from '@/lib/notifications/sound-manager'

export function useNotificationSounds() {
  const soundManager = SoundManager.getInstance()

  // Initialize sounds on component mount
  useEffect(() => {
    const handleUserInteraction = () => {
      initializeSounds()
      // Remove listeners after first interaction
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('keydown', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
    }

    // Add listeners for first user interaction
    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('keydown', handleUserInteraction)
    document.addEventListener('touchstart', handleUserInteraction)

    return () => {
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('keydown', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
    }
  }, [])

  const playPayment = useCallback(async () => {
    await playPaymentSound()
  }, [])

  const playMessage = useCallback(async () => {
    await playMessageSound()
  }, [])

  const playCreation = useCallback(async () => {
    await playCreationSound()
  }, [])

  const toggleSounds = useCallback((enabled: boolean) => {
    soundManager.setEnabled(enabled)
  }, [soundManager])

  const isSoundsEnabled = useCallback(() => {
    return soundManager.isNotificationEnabled()
  }, [soundManager])

  return {
    playPayment,
    playMessage,
    playCreation,
    toggleSounds,
    isSoundsEnabled
  }
}
