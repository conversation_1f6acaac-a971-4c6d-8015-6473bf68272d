-- Clean up legacy subscriber_id column from subscriptions table
-- This column was renamed to reader_id but still exists as nullable, causing confusion

-- First, ensure any data in subscriber_id is migrated to reader_id
UPDATE subscriptions
SET reader_id = subscriber_id
WHERE subscriber_id IS NOT NULL
  AND reader_id IS NULL;

-- Fix policies that depend on subscriber_id before dropping the column

-- 1. Fix voice_posts policy
DROP POLICY IF EXISTS "Users can view voice posts they have access to" ON voice_posts;
CREATE POLICY "Users can view voice posts they have access to" ON voice_posts
    FOR SELECT USING (
        is_free = true
        OR auth.uid() = user_id
        OR EXISTS (
            SELECT 1 FROM subscriptions
            WHERE reader_id = auth.uid()
            AND writer_id = voice_posts.user_id
            AND status = 'active'
            AND current_period_end > NOW()
        )
    );

-- 2. Fix subscriber_favorites policy (this table should probably use reader_id too)
DROP POLICY IF EXISTS "Users can manage their own favorites" ON subscriber_favorites;
CREATE POLICY "Users can manage their own favorites" ON subscriber_favorites
    FOR ALL USING (auth.uid() = subscriber_id);

-- 3. Fix creator_notification_subscriptions policy
DROP POLICY IF EXISTS "Users can manage their own subscriptions" ON creator_notification_subscriptions;
CREATE POLICY "Users can manage their own subscriptions" ON creator_notification_subscriptions
    FOR ALL USING (auth.uid() = subscriber_id);

-- Now we can safely drop the legacy subscriber_id column from subscriptions
ALTER TABLE subscriptions DROP COLUMN IF EXISTS subscriber_id;

-- Update any RLS policies to ensure they work correctly with reader_id
DROP POLICY IF EXISTS "Users can view their subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can manage their subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Readers can manage their subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Writers can view their subscriptions" ON subscriptions;

-- Create clean, consistent RLS policies for subscriptions table
CREATE POLICY "Readers can manage their subscriptions" ON subscriptions
    FOR ALL USING (auth.uid() = reader_id);

CREATE POLICY "Writers can view their subscriptions" ON subscriptions
    FOR SELECT USING (auth.uid() = writer_id);

-- Success message
SELECT 'Legacy subscriber_id column cleaned up successfully!' as status;
