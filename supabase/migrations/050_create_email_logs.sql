-- Create email_logs table for tracking sent emails
CREATE TABLE IF NOT EXISTS email_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_type TEXT NOT NULL, -- 'welcome', 'confirmation-reminder', 'password-reset', etc.
    recipient_email TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'sent', -- 'sent', 'failed', 'bounced'
    resend_message_id TEXT,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient_email ON email_logs(recipient_email);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at DESC);

-- Create composite index for checking recent emails to prevent spam
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient_type_created ON email_logs(recipient_email, email_type, created_at DESC);

-- Add comment explaining the table
COMMENT ON TABLE email_logs IS 'Tracks all emails sent through the system for monitoring and anti-spam purposes';
COMMENT ON COLUMN email_logs.email_type IS 'Type of email sent (welcome, confirmation-reminder, password-reset, etc.)';
COMMENT ON COLUMN email_logs.metadata IS 'Additional data about the email (template version, campaign info, etc.)';
