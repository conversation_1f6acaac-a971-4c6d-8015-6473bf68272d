-- Final subscription system fixes to ensure consistency
-- This ensures all subscription checking uses the correct column names and logic

-- Update the main subscription function to use current schema
CREATE OR REPLACE FUNCTION user_has_active_subscription(subscriber_uuid UUID, writer_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.subscriptions
        WHERE reader_id = subscriber_uuid
        AND writer_id = writer_uuid
        AND status = 'active'
        AND current_period_end > NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Grant permissions
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_active_subscription(UUID, UUID) TO anon;

-- Update the get_entry_preview function to use the corrected subscription check
CREATE OR REPLACE FUNCTION get_entry_preview(
    entry_id UUID,
    viewer_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    title TEXT,
    body_md TEXT,
    is_free BOOLEAN,
    is_hidden BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    user_id UUID,
    writer_id UUID,
    writer_name TEXT,
    writer_custom_url TEXT,
    writer_price INTEGER,
    can_read_full BOOLEAN,
    love_count INTEGER
) AS $$
DECLARE
    entry_record RECORD;
    can_read BOOLEAN := FALSE;
BEGIN
    -- Get the entry and writer info
    SELECT 
        de.id,
        de.title,
        de.body_md,
        de.is_free,
        de.is_hidden,
        de.created_at,
        de.updated_at,
        de.user_id,
        u.name as writer_name,
        u.custom_url as writer_custom_url,
        u.price_monthly,
        de.love_count
    INTO entry_record
    FROM public.diary_entries de
    JOIN public.users u ON u.id = de.user_id
    WHERE de.id = entry_id;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    -- Check if user can read full content
    IF entry_record.is_free THEN
        can_read := TRUE;
    ELSIF viewer_id IS NOT NULL THEN
        -- Check if viewer is the author
        IF viewer_id = entry_record.user_id THEN
            can_read := TRUE;
        ELSE
            -- Check subscription using the updated function
            can_read := user_has_active_subscription(viewer_id, entry_record.user_id);
        END IF;
    END IF;
    
    -- Return the entry data
    RETURN QUERY SELECT
        entry_record.id,
        entry_record.title,
        entry_record.body_md,
        entry_record.is_free,
        entry_record.is_hidden,
        entry_record.created_at,
        entry_record.updated_at,
        entry_record.user_id,
        entry_record.user_id as writer_id,
        entry_record.writer_name,
        entry_record.writer_custom_url,
        entry_record.price_monthly as writer_price,
        can_read as can_read_full,
        entry_record.love_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_entry_preview(UUID, UUID) TO anon;

-- Ensure the subscriptions table has proper indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_reader_writer_active ON subscriptions(reader_id, writer_id, status, current_period_end);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_id ON subscriptions(stripe_subscription_id);

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Final subscription system fixes completed successfully!';
END $$;
