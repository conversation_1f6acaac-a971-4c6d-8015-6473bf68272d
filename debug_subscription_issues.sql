-- Debug SQL queries to check subscription system issues

-- 1. Check subscriptions table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'subscriptions' 
ORDER BY ordinal_position;

-- 2. Check if subscriptions table exists and has data
SELECT 
    COUNT(*) as total_subscriptions,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_subscriptions,
    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_subscriptions
FROM subscriptions;

-- 3. Check RLS policies on subscriptions table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'subscriptions';

-- 4. Check if RPC functions exist
SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'get_next_entry',
    'get_previous_entry', 
    'update_reading_position'
);

-- 5. Check users table structure for subscription-related fields
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN (
    'price_monthly',
    'stripe_account_id', 
    'stripe_onboarding_complete',
    'subscriber_count'
)
ORDER BY column_name;

-- 6. Check specific user data for the failing subscription
SELECT 
    id,
    name,
    price_monthly,
    stripe_account_id,
    stripe_onboarding_complete,
    subscriber_count
FROM users 
WHERE id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 7. Check existing subscriptions for the user trying to subscribe
SELECT 
    s.*,
    w.name as writer_name,
    r.name as reader_name
FROM subscriptions s
LEFT JOIN users w ON s.writer_id = w.id
LEFT JOIN users r ON s.reader_id = r.id
WHERE s.reader_id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d'
   OR s.writer_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e';

-- 8. Check diary entries for paid content
SELECT 
    COUNT(*) as total_entries,
    COUNT(CASE WHEN is_free = false THEN 1 END) as paid_entries,
    COUNT(CASE WHEN is_free = true THEN 1 END) as free_entries
FROM diary_entries 
WHERE writer_id = 'aaa7e8be-277c-459e-87aa-c4b736cc143e'
AND is_published = true;

-- 9. Check creator_notification_subscriptions table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'creator_notification_subscriptions' 
ORDER BY ordinal_position;

-- 10. Check RLS policies on creator_notification_subscriptions
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'creator_notification_subscriptions';

-- 11. Check if there are any foreign key constraints issues
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('subscriptions', 'creator_notification_subscriptions');

-- 12. Check if the legacy subscriber_id column still exists
SELECT 
    column_name
FROM information_schema.columns 
WHERE table_name = 'subscriptions' 
AND column_name = 'subscriber_id';

-- 13. Test RLS access for the specific user
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claims TO '{"sub": "f296c9e6-f15d-402e-ab58-eebdc1947e1d"}';

-- Try to query subscriptions as the user
SELECT COUNT(*) as accessible_subscriptions
FROM subscriptions 
WHERE reader_id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d';

RESET ROLE;

-- 14. Check for any triggers on subscriptions table
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'subscriptions';
