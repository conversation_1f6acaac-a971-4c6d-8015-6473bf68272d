/**
 * OnlyDiary Sound Notification Manager
 * Handles playing notification sounds for payments and messages
 */

export class SoundManager {
  private static instance: SoundManager
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()
  private isEnabled: boolean = true

  private constructor() {
    // Initialize audio context on first user interaction
    this.initializeAudioContext()
  }

  static getInstance(): SoundManager {
    if (!SoundManager.instance) {
      SoundManager.instance = new SoundManager()
    }
    return SoundManager.instance
  }

  private async initializeAudioContext() {
    if (typeof window === 'undefined') return

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // Preload sounds
      await this.preloadSounds()
    } catch (error) {
      console.warn('Audio context initialization failed:', error)
    }
  }

  private async preloadSounds() {
    // Try multiple possible locations and filenames for the sound files
    const soundFiles = {
      payment: [
        '/payment-notification.mp3',
        '/sounds/payment-notification.mp3',
        '/payment.mp3',
        '/payment-sound.mp3'
      ],
      message: [
        '/message-notification.mp3',
        '/sounds/message-notification.mp3',
        '/message.mp3',
        '/message-sound.mp3'
      ],
      creation: [
        '/create_result.mp3',
        '/sounds/create_result.mp3',
        '/creation.mp3',
        '/create.mp3'
      ]
    }

    for (const [key, urls] of Object.entries(soundFiles)) {
      let loaded = false

      for (const url of urls) {
        try {
          console.log(`Trying to load ${key} sound from: ${url}`)
          const response = await fetch(url)

          if (!response.ok) {
            console.log(`${url} not found (${response.status})`)
            continue
          }

          const arrayBuffer = await response.arrayBuffer()

          if (this.audioContext) {
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
            this.sounds.set(key, audioBuffer)
            console.log(`✅ Successfully loaded ${key} sound from: ${url}`)
            loaded = true
            break
          }
        } catch (error) {
          console.log(`Failed to load ${key} sound from ${url}:`, error)
        }
      }

      if (!loaded) {
        console.warn(`❌ Could not load ${key} sound from any location`)
      }
    }
  }

  async playPaymentNotification() {
    await this.playSound('payment')
  }

  async playMessageNotification() {
    await this.playSound('message')
  }

  async playCreationNotification() {
    await this.playSound('creation')
  }

  private async playSound(soundKey: string) {
    if (!this.isEnabled || !this.audioContext || !this.sounds.has(soundKey)) {
      return
    }

    try {
      // Resume audio context if suspended (required by browser policies)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      const audioBuffer = this.sounds.get(soundKey)
      if (!audioBuffer) return

      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()
      
      source.buffer = audioBuffer
      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      // Set volume (0.0 to 1.0)
      gainNode.gain.value = 0.7
      
      source.start()
    } catch (error) {
      console.warn(`Failed to play ${soundKey} sound:`, error)
    }
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    
    // Store preference in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('onlydiary-sounds-enabled', enabled.toString())
    }
  }

  isNotificationEnabled(): boolean {
    if (typeof window === 'undefined') return true
    
    const stored = localStorage.getItem('onlydiary-sounds-enabled')
    return stored !== null ? stored === 'true' : true
  }

  // Initialize on first user interaction
  async initializeOnUserInteraction() {
    if (!this.audioContext) {
      await this.initializeAudioContext()
    }
  }
}

// Convenience functions for easy use throughout the app
export const playPaymentSound = () => SoundManager.getInstance().playPaymentNotification()
export const playMessageSound = () => SoundManager.getInstance().playMessageNotification()
export const playCreationSound = () => SoundManager.getInstance().playCreationNotification()
export const initializeSounds = () => SoundManager.getInstance().initializeOnUserInteraction()
