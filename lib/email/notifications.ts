import { sendEmail, EmailTemplates } from './resend'
import { createSupabaseClient } from '@/lib/supabase/client'

// Send welcome email to new users
export async function sendWelcomeEmail(userId: string, userEmail: string, userName: string, userRole: 'subscriber' | 'writer') {
  try {
    const template = EmailTemplates.welcome(userName, userRole)

    const result = await sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tag: template.tag
    })

    if (result.success) {
      // Log the email send in database (optional)
      try {
        const supabase = createSupabaseClient()
        await supabase.from('email_logs').insert({
          user_id: userId,
          email_type: 'welcome',
          recipient_email: userEmail,
          status: 'sent',
          resend_message_id: result.id
        })
      } catch (err) {
        console.log('Failed to log email (non-critical):', err)
      }
    }

    return result
  } catch (error) {
    console.error('Failed to send welcome email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Send confirmation reminder email to unconfirmed users
export async function sendConfirmationReminder(userId: string, userEmail: string, userName: string) {
  try {
    // Check if we've sent a reminder recently (within 24 hours)
    const supabase = createSupabaseClient()
    const oneDayAgo = new Date()
    oneDayAgo.setHours(oneDayAgo.getHours() - 24)

    const { data: recentReminders, error: checkError } = await supabase
      .from('email_logs')
      .select('created_at')
      .eq('recipient_email', userEmail)
      .eq('email_type', 'confirmation-reminder')
      .gte('created_at', oneDayAgo.toISOString())
      .limit(1)

    if (checkError) {
      console.error('Error checking recent reminders:', checkError)
      return { success: false, error: 'Failed to check recent emails' }
    }

    if (recentReminders && recentReminders.length > 0) {
      console.log(`Skipping reminder for ${userEmail} - sent recently`)
      return { success: false, error: 'Reminder already sent recently' }
    }

    const template = EmailTemplates.confirmationReminder(userName)

    const result = await sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tag: template.tag
    })

    // Log the email attempt
    try {
      await supabase.from('email_logs').insert({
        user_id: userId,
        email_type: 'confirmation-reminder',
        recipient_email: userEmail,
        status: result.success ? 'sent' : 'failed',
        resend_message_id: result.id,
        error_message: result.success ? null : result.error,
        metadata: {
          userName,
          manualTrigger: true
        }
      })
    } catch (err) {
      console.log('Failed to log confirmation reminder (non-critical):', err)
    }

    return result
  } catch (error) {
    console.error('Failed to send confirmation reminder:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Send password reset email
export async function sendPasswordResetEmail(userEmail: string, resetLink: string) {
  try {
    const template = EmailTemplates.passwordReset(resetLink)
    
    const result = await sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tag: template.tag
    })

    return result
  } catch (error) {
    console.error('Failed to send password reset email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Send new post notification to subscribers
export async function sendNewPostNotification(
  subscriberEmail: string, 
  subscriberName: string,
  writerName: string, 
  postTitle: string, 
  postUrl: string
) {
  try {
    const result = await sendEmail({
      to: subscriberEmail,
      subject: `${writerName} just published: ${postTitle}`,
      htmlBody: `
        <div style="font-family: Georgia, serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
          <div style="background-color: #f9fafb; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #1f2937; font-size: 20px; margin: 0 0 15px 0;">New from ${writerName} ✨</h2>
            <p style="color: #374151; line-height: 1.6; margin: 0 0 15px 0;">
              Hi ${subscriberName}! ${writerName} just published a new story you won't want to miss:
            </p>
            <h3 style="color: #1f2937; font-size: 18px; margin: 15px 0;">"${postTitle}"</h3>
            
            <div style="text-align: center; margin: 25px 0;">
              <a href="${postUrl}" 
                 style="background-color: #1f2937; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
                Read Now
              </a>
            </div>
          </div>
          
          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/settings/notifications" style="color: #1f2937;">Manage email preferences</a>
            </p>
          </div>
        </div>
      `,
      textBody: `
New from ${writerName}

Hi ${subscriberName}! ${writerName} just published a new story:

"${postTitle}"

Read it here: ${postUrl}

Manage your email preferences: ${process.env.NEXT_PUBLIC_SITE_URL}/settings/notifications
      `,
      tag: 'new-post'
    })

    return result
  } catch (error) {
    console.error('Failed to send new post notification:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Send weekly digest email
export async function sendWeeklyDigest(
  subscriberEmail: string,
  subscriberName: string,
  posts: Array<{
    title: string
    writerName: string
    url: string
    publishedAt: string
  }>
) {
  try {
    const postsHtml = posts.map(post => `
      <div style="border-bottom: 1px solid #e5e7eb; padding: 15px 0;">
        <h4 style="color: #1f2937; margin: 0 0 5px 0; font-size: 16px;">
          <a href="${post.url}" style="color: #1f2937; text-decoration: none;">${post.title}</a>
        </h4>
        <p style="color: #6b7280; margin: 0; font-size: 14px;">by ${post.writerName}</p>
      </div>
    `).join('')

    const postsText = posts.map(post => 
      `"${post.title}" by ${post.writerName}\n${post.url}\n`
    ).join('\n')

    const result = await sendEmail({
      to: subscriberEmail,
      subject: `Your weekly OnlyDiary digest - ${posts.length} new stories`,
      htmlBody: `
        <div style="font-family: Georgia, serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1f2937; font-size: 24px; margin: 0;">Your Weekly Digest</h1>
            <p style="color: #6b7280; font-size: 16px; margin: 10px 0 0 0;">${posts.length} new stories from your favorite creators</p>
          </div>
          
          <div style="background-color: #f9fafb; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <p style="color: #374151; line-height: 1.6; margin: 0 0 20px 0;">
              Hi ${subscriberName}! Here's what you missed this week:
            </p>
            ${postsHtml}
          </div>
          
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/timeline" 
               style="background-color: #1f2937; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
              View All Stories
            </a>
          </div>
          
          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/settings/notifications" style="color: #1f2937;">Manage email preferences</a>
            </p>
          </div>
        </div>
      `,
      textBody: `
Your Weekly OnlyDiary Digest

Hi ${subscriberName}! Here's what you missed this week:

${postsText}

View all stories: ${process.env.NEXT_PUBLIC_SITE_URL}/timeline

Manage your email preferences: ${process.env.NEXT_PUBLIC_SITE_URL}/settings/notifications
      `,
      tag: 'weekly-digest'
    })

    return result
  } catch (error) {
    console.error('Failed to send weekly digest:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Test email function
export async function sendTestEmail(toEmail: string) {
  try {
    const template = EmailTemplates.test()

    const result = await sendEmail({
      to: toEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tag: template.tag
    })

    return result
  } catch (error) {
    console.error('Failed to send test email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
