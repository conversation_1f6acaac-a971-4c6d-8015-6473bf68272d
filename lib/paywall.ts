/**
 * Paywall utility functions for OnlyDiary
 * Handles access control, content truncation, and subscription checking
 */

export interface PaywallContent {
  truncatedContent: string
  remainingWords: number
  hasAccess: boolean
  needsSubscription: boolean
}

/**
 * Check if user has access to paid content
 */
export function checkContentAccess(
  isFree: boolean,
  isOwner: boolean,
  hasSubscription: boolean
): boolean {
  // Free content is always accessible
  if (isFree) return true
  
  // Owner can always see their own content
  if (isOwner) return true
  
  // Paid content requires subscription
  return hasSubscription
}

/**
 * Process content for paywall display
 */
export function processPaywallContent(
  content: string,
  hasAccess: boolean
): PaywallContent {
  if (hasAccess) {
    return {
      truncatedContent: content,
      remainingWords: 0,
      hasAccess: true,
      needsSubscription: false
    }
  }

  // Split content into sentences
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
  
  if (sentences.length === 0) {
    return {
      truncatedContent: "",
      remainingWords: 0,
      hasAccess: false,
      needsSubscription: true
    }
  }

  // Show only first sentence
  const firstSentence = sentences[0].trim() + "."
  
  // Calculate remaining words
  const allWords = content.trim().split(/\s+/).filter(word => word.length > 0)
  const shownWords = firstSentence.trim().split(/\s+/).filter(word => word.length > 0)
  const remainingWords = Math.max(0, allWords.length - shownWords.length)

  return {
    truncatedContent: firstSentence,
    remainingWords,
    hasAccess: false,
    needsSubscription: true
  }
}

/**
 * Get subscription status between two users
 */
export async function getSubscriptionStatus(
  supabase: any,
  subscriberId: string | null,
  writerId: string
): Promise<boolean> {
  if (!subscriberId || subscriberId === writerId) {
    return subscriberId === writerId // Owner has access
  }

  const { data: subscription } = await supabase
    .from('subscriptions')
    .select('status')
    .eq('reader_id', subscriberId)
    .eq('writer_id', writerId)
    .eq('status', 'active')
    .gte('current_period_end', new Date().toISOString())
    .single()

  return !!subscription
}

/**
 * Check if user has purchased a specific book project
 */
export async function getProjectPurchaseStatus(
  supabase: any,
  userId: string | null,
  projectId: string,
  writerId: string
): Promise<boolean> {
  if (!userId || userId === writerId) {
    return userId === writerId // Owner has access
  }

  const { data: purchase } = await supabase
    .from('project_purchases')
    .select('id')
    .eq('buyer_id', userId)
    .eq('project_id', projectId)
    .single()

  return !!purchase
}
